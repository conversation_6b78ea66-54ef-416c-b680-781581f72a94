import type { <PERSON>s, Meta } from "./response";

export interface BlogPost {
  id: number;
  title: string;
  slug: string;
  short_description: string;
  image: string;
  blogCategoryTitle: string;
  blogCategorySlug: string;
  createdByUserName: string;
  created_at: string;
}

export interface BlogCategory {
  id: number;
  title: string;
  slug: string;
  status: boolean;
  created_by?: number;
  updated_by: number;
  created_at: string;
  updated_at: string;
}

export interface CreatedBy {
  id: number;
  name: string;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  avatar: string | null;
  status: string;
}

export interface BlogDetails {
  id: number;
  user_id: number;
  title: string;
  slug: string;
  short_description: string;
  content: string;
  image: string;
  reading_time_minutes: string;
  status: boolean;
  blogCategory: BlogCategory;
  created_by: CreatedBy;
  created_at: string;
}

export interface BlogCategoryResponse {
  data: BlogCategory[];
  links?: Links;
  meta?: Meta;
}

export interface BlogResponse {
  data: BlogPost[];
  links?: Links;
  meta?: Meta;
}

export interface BlogDetailsResponse {
  data: BlogDetails;
}
