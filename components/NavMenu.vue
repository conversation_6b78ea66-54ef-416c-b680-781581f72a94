<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAuthStore } from "~/stores/auth";

defineProps<{
  iosPayment: boolean;
  isActivePage: (
    pages: String[],
    activeClass: String,
    inactiveClass: String
  ) => String;
  closeMenu: () => void;
}>();

const localePath = useLocalePath();
const { isLoggedIn } = storeToRefs(useAuthStore());
</script>

<template>
  <div>
    <ul
      class="list-style-none ml-auto flex flex-col pl-0 lg:flex-row flex-wrap justify-end"
    >
      <li
        class="menu-item"
        :class="isActivePage(['index'], 'border-primary-red', 'border-white')"
      >
        <NuxtLink
          @click="closeMenu"
          class="nav-link-custom"
          :class="isActivePage(['index'], 'text-primary-red', '')"
          aria-current="page"
          :to="localePath('/')"
          >{{ $t("home") }}</NuxtLink
        >
      </li>
      <li
        class="menu-item"
        :class="isActivePage(['blog'], 'border-primary-red', 'border-white')"
      >
        <NuxtLink
          @click="closeMenu"
          class="nav-link-custom"
          :class="isActivePage(['blog'], 'text-primary-red', '')"
          aria-current="page"
          :to="localePath('/blog')"
          >Blog</NuxtLink
        >
      </li>
      <li
        class="menu-item"
        :class="
          isActivePage(['success-story'], 'border-primary-red', 'border-white')
        "
      >
        <NuxtLink
          @click="closeMenu"
          class="nav-link-custom"
          :class="isActivePage(['success-story'], 'text-primary-red', '')"
          aria-current="page"
          :to="localePath('/success-story')"
          >{{ $t("success_Story") }}</NuxtLink
        >
      </li>
      <li
        class="menu-item"
        :class="
          isActivePage(['firmware'], 'border-primary-red', 'border-white')
        "
      >
        <NuxtLink
          @click="closeMenu"
          class="nav-link-custom"
          :class="isActivePage(['firmware'], 'text-primary-red', '')"
          aria-current="page"
          :to="localePath('/firmware')"
          >Flash File/Tools</NuxtLink
        >
      </li>
      <li
        class="menu-item"
        :class="
          isActivePage(['communication'], 'border-primary-red', 'border-white')
        "
      >
        <NuxtLink
          @click="closeMenu"
          class="nav-link-custom"
          :class="isActivePage(['communication'], 'text-primary-red', '')"
          aria-current="page"
          :to="localePath('/communication')"
          >{{ $t("communication") }}</NuxtLink
        >
      </li>
      <li
        class="menu-item"
        :class="
          isActivePage(
            ['online-services'],
            'border-primary-red',
            'border-white'
          )
        "
      >
        <NuxtLink
          @click="closeMenu"
          class="nav-link-custom"
          :class="isActivePage(['online-services'], 'text-primary-red', '')"
          aria-current="page"
          :to="localePath('/online-services')"
          >{{ $t("online_services") }}</NuxtLink
        >
      </li>
      <li
        class="py-2 mx-2 self-center lg:border-b-4"
        :class="
          isActivePage(['browse-course'], 'border-primary-red', 'border-white')
        "
      >
        <NuxtLink
          @click="closeMenu"
          type="button"
          :to="localePath('/browse-course')"
          class="animate-button red-button text-sm px-4"
        >
          {{ $t("browse_course") }}
        </NuxtLink>
      </li>
      <li
        class="py-2 lg:ml-2 xl:ml-12 self-center lg:border-b-4"
        :class="
          isActivePage(
            ['auth-login', 'dashboard'],
            'border-primary-red',
            'border-white'
          )
        "
      >
        <NuxtLink
          v-if="!isLoggedIn"
          class="red-button text-sm"
          :to="localePath('/auth/login')"
          @click="closeMenu"
        >
          <span>{{ $t("login") }}</span>
        </NuxtLink>
        <NuxtLink
          v-else
          class="red-button text-sm"
          :to="localePath('/dashboard')"
          @click="closeMenu"
        >
          <span> {{ $t("dashboard") }}</span>
        </NuxtLink>
      </li>
    </ul>
  </div>
</template>

<style scoped>
.menu-item {
  @apply mx-2 py-4 self-center w-full lg:w-fit text-center whitespace-nowrap lg:border-b-4;
}
@media screen and (min-width: 1024px) and (max-width: 1200px) {
  .menu-item {
    @apply mx-1;
  }
}
</style>
