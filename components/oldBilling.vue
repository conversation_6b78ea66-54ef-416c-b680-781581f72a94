<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useCartDataStore } from "~/stores/cartData";
import { useSubscriptionPackageStore } from "~/stores/subscriptionPackage";

const props = defineProps({
  currentBillingComp: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["goToManualPayment"]);

const { setAllCartData, setCartDataForApi } = useCartDataStore();
const { setSubscriptionPackageId } = useSubscriptionPackageStore();
const { allCartData, subscriptionDataForApi } = storeToRefs(useCartDataStore());
const { subscriptionPackageId } = storeToRefs(useSubscriptionPackageStore());

const { apiBaseUrl } = useUrls();
const localePath = useLocalePath();
const nuxtApp = useNuxtApp();
const route = useRoute();
const { setRedirectUrl } = useAuth();
const router = useRouter();
const { t } = useI18n();
const tokenCookie = useCookie("token");

const payMethod = ref("bkash");
const isOrderBtnDisabled = ref(false);
const termsAccepted = ref(false);
const methodAccepted = ref(true);
const showFileModal = ref(false);
const isManualPayment = ref(true);

const placeOrder = async () => {
  isOrderBtnDisabled.value = true;
  window.removeEventListener("beforeunload", beforeunload);
  if (
    !tokenCookie.value ||
    tokenCookie.value === "" ||
    tokenCookie.value === undefined
  ) {
    setRedirectUrl(route.fullPath);
    router.push(localePath("/auth/login"));
    nuxtApp.$toast("clear");
    nuxtApp.$toast("error", {
      message: t("messages.login_first"),
      className: "toasted-bg-alert",
    });
  } else {
    if (payMethod.value !== "") {
      if (
        allCartData.value.length > 0 &&
        props.currentBillingComp === "video"
      ) {
        const orderItemDetails = [
          {
            item_id: allCartData.value[0]?.item?.id,
            subscription_id: allCartData.value[0]?.subscription?.id,
          },
        ];
        try {
          const { data } = await useFetch<any>(`${apiBaseUrl}/order`, {
            method: "POST",
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
            },
            body: {
              code: allCartData.value.code ? allCartData.value.code : "",
              type: "video",
              payment_method: payMethod.value,
              items: orderItemDetails,
            },
          });
          if (!data.value.error) {
            nuxtApp.$toast("clear");
            nuxtApp.$toast("success", {
              message: data.value.message,
              className: "toasted-bg-success",
            });
            window.open(data.value.url, "_self");
            setTimeout(() => {
              setAllCartData([]);
              setCartDataForApi([]);
            }, 400);
          }
        } catch (error: any) {
          isOrderBtnDisabled.value = false;
          window.addEventListener("beforeunload", beforeunload);
          nuxtApp.$toast("clear");
          nuxtApp.$toast("error", {
            message: error?.response?._data?.message,
            className: "toasted-bg-alert",
          });
        }
      } else if (props.currentBillingComp === "file") {
        const fileDetails = subscriptionDataForApi.value.downloadFileDetails;
        setRedirectUrl(`/download/${fileDetails.slug}`);
        try {
          const { data } = await useFetch<any>(`${apiBaseUrl}/order`, {
            method: "POST",
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
            },
            body: {
              code: "",
              type: `${subscriptionDataForApi.value.type === "file"}`
                ? `${subscriptionDataForApi.value.type}`
                : `${subscriptionPackageId.value[0]?.subscriptionFrom}`,
              payment_method: payMethod.value,
              items: `${subscriptionDataForApi.value.type === "file"}`
                ? [{ item_id: fileDetails.id }]
                : [
                    {
                      subscription_id:
                        subscriptionPackageId.value[0].subscription_id,
                    },
                  ],
            },
          });

          if (!data.value.error) {
            nuxtApp.$toast("clear");
            nuxtApp.$toast("success", {
              message: data.value.message,
              className: "toasted-bg-success",
            });
            window.open(data.value.url, "_self");
            setTimeout(() => {
              setAllCartData([]);
              setCartDataForApi([]);
              setSubscriptionPackageId([]);
            }, 400);
          }
        } catch (error: any) {
          isOrderBtnDisabled.value = false;
          window.addEventListener("beforeunload", beforeunload);
          nuxtApp.$toast("clear");
          nuxtApp.$toast("error", {
            message: error.response?.message,
            className: "toasted-bg-alert",
          });
        }
      }
    }
  }
};

const beforeunload = (e: any) => {
  e.preventDefault();
  e.returnValue = "Data will be lost if you reload the page.";
};

const handleManualPayment = () => {
  emit("goToManualPayment");
  setTimeout(() => {
   window.scrollTo(0, 0); 
  });
};

onMounted(() => {
  window.addEventListener("beforeunload", beforeunload);
});
onUnmounted(() => {
  window.removeEventListener("beforeunload", beforeunload);
});
</script>

<template>
  <div
    class="w-full md:w-4/5 xl:w-2/3 pt-[30px] md:pt-[70px] xl:pt-20 pb-20 xl:pb-36 mx-auto"
  >
    <div
      v-if="
        !isOrderBtnDisabled &&
        (allCartData.length > 0 ||
          subscriptionPackageId[0]?.subscriptionFrom === 'subscription' ||
          subscriptionDataForApi.type === 'file')
      "
      class="pt-10 md:pt-20 pb-10"
    >
      <div
        v-if="allCartData.length > 0 && currentBillingComp === 'video'"
        class="overflow-x-auto pt-5"
      >
        <div v-for="(item, index) in allCartData">
          <div class="border-2 border-black-600 px-4 lg:px-[40px] pb-[20px]">
            <div class="hidden">
              <p class="text-base md:text-[20px] font-semibold">
                {{ $t("order_id") }}: TMS21696843
              </p>
            </div>
            <div class="flex justify-between mt-10">
              <p class="text-base md:text-[20px] line-clamp-1">
                {{ item.item.title }}
              </p>
              <p class="text-base md:text-[20px] font-semibold">
                {{
                  item?.item?.special_price > 0
                    ? $bdNumberFormat(Number(item.item.special_price))
                    : $bdNumberFormat(Number(item.item.price))
                }}
              </p>
            </div>
            <div class="flex justify-between my-[20px]">
              <p class="text-base md:text-[20px]">
                {{ $t("subcription") }}
              </p>
              <p class="text-base md:text-[20px]">
                ৳{{ allCartData.subscriptionPrice.toFixed(2) }}
              </p>
            </div>
            <div
              class="flex justify-between border-b border-black my-[20px] pb-[40px]"
            >
              <p class="text-base md:text-[20px]">
                {{ $t("coupon_discount") }}
              </p>
              <p class="text-base md:text-[20px] text-primary-red">
                -৳{{ allCartData.couponDiscount }}
              </p>
            </div>
            <div class="flex justify-between">
              <p class="text-base md:text-[20px] font-semibold">
                {{ $t("total") }}
              </p>
              <p class="text-base md:text-[20px] font-semibold">
                ৳{{
                  (
                    allCartData.grandTotal -
                    (allCartData.couponDiscount !== 0
                      ? allCartData.couponDiscount.replace(/,/g, "")
                      : allCartData.couponDiscount)
                  ).toFixed(2)
                }}
              </p>
            </div>
            <div class="flex justify-between mt-[50px] items-cente_r">
              <p class="text-base md:text-[20px] font-semibold">
                {{ $t("select_payment_method") }}
              </p>
              <div
                class="flex items-center border px-[16px] py-[8px] rounded-3xl"
              >
                <div class="p-3 bg-primary-red rounded-3xl">
                  <img
                    class="w-3 h-[14px]"
                    src="/images/billing-methods/Layer_1.png"
                    alt=""
                  />
                </div>
                <p class="text-sm sm:text-[16px] ml-5">
                  {{ $t("completely_secured") }}
                </p>
              </div>
            </div>
            <div
              class="flex justify-between border border-black rounded-xl mt-[50px] py-3 px-6"
            >
              <div class="flex items-center">
                <input
                  v-model="methodAccepted"
                  type="radio"
                  id="radioButton"
                  name="radioButton"
                  :value="true"
                  class="h-5 w-5 text-blue-500 focus:ring-blue-400 border-gray-300 rounded-md"
                />
                <label for="radioButton" class="ml-5 text-gray-700">{{
                  $t("payment_method")
                }}</label>
              </div>
              <img
                class="w-20 aspect-video"
                src="/images/billing-methods/payment.svg"
              />
            </div>
            <div class="flex items-start gap-4 pt-4">
              <input
                type="checkbox"
                v-model="termsAccepted"
                class="w-6 h-6 aspect-square"
              />
              <p class="font-semibold">
                {{ $t("billing_policy_text") }}
                <NuxtLink
                  :to="localePath('/privacy-policy')"
                  target="_blank"
                  class="text-primary-red"
                >
                  {{ $t("privacy_policy") }} </NuxtLink
                >,
                <NuxtLink
                  :to="localePath('/terms-of-use')"
                  target="_blank"
                  class="text-primary-red"
                >
                  {{ $t("terms_conditions") }} </NuxtLink
                >,
                <NuxtLink
                  :to="localePath('/refund-policy')"
                  target="_blank"
                  class="text-primary-red"
                >
                  {{ $t("refund_policy") }}
                </NuxtLink>
                {{ $t("and") }}
                <NuxtLink
                  :to="localePath('/cancellation-policy')"
                  target="_blank"
                  class="text-primary-red"
                >
                  {{ $t("cancellation_policy") }} </NuxtLink
                >.
              </p>
            </div>
            <div class="flex p-5 w-full">
              <button
                v-if="!isManualPayment"
                class="outline-none text-white font-semibold py-2 md:py-3 rounded-sm w-full"
                :class="
                  (allCartData.length === 0 &&
                    subscriptionPackageId.length === 0 &&
                    subscriptionDataForApi.type !== 'file') ||
                  isOrderBtnDisabled ||
                  !termsAccepted ||
                  !methodAccepted
                    ? 'bg-red-300'
                    : 'bg-primary-red'
                "
                type="button"
                :disabled="
                  (allCartData.length === 0 &&
                    subscriptionPackageId.length === 0 &&
                    subscriptionDataForApi.type !== 'file') ||
                  isOrderBtnDisabled ||
                  !termsAccepted ||
                  !methodAccepted
                "
                @click="placeOrder"
              >
                {{ $t("place_order_btn") }}
              </button>
              <button
                v-else
                class="outline-none text-white font-semibold py-2 md:py-3 rounded-sm w-full"
                :class="
                  (allCartData.length === 0 &&
                    subscriptionPackageId.length === 0 &&
                    subscriptionDataForApi.type !== 'file') ||
                  isOrderBtnDisabled ||
                  !termsAccepted ||
                  !methodAccepted
                    ? 'bg-red-300'
                    : 'bg-primary-red'
                "
                type="button"
                :disabled="
                  (allCartData.length === 0 &&
                    subscriptionPackageId.length === 0 &&
                    subscriptionDataForApi.type !== 'file') ||
                  isOrderBtnDisabled ||
                  !termsAccepted ||
                  !methodAccepted
                "
                @click="handleManualPayment"
              >
                {{ $t("Next") }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div
        v-else-if="
          subscriptionPackageId[0]?.subscriptionFrom === 'subscription'
        "
      >
        <table
          class="w-full table-auto border-collapse border border-black whitespace-nowrap text-black"
        >
          <thead class="font-semibold">
            <tr>
              <th class="">Name</th>
              <th class="">Duration (in months)</th>
              <th class="">Amount</th>
              <th class="">Band Width</th>
              <th class="">Total File</th>
              <th class="">Type</th>
              <th class="">Usage</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>{{ subscriptionPackageId[0].subscriptionPackage.name }}</td>
              <td>
                {{
                  subscriptionPackageId[0].subscriptionPackage
                    .durations_as_month
                }}
              </td>
              <td>
                {{ subscriptionPackageId[0].subscriptionPackage.amount }}
              </td>
              <td>
                {{ subscriptionPackageId[0].subscriptionPackage.band_width }}
              </td>
              <td>
                {{ subscriptionPackageId[0].subscriptionPackage.total_file }}
              </td>
              <td>
                {{ subscriptionPackageId[0].subscriptionPackage.type }}
              </td>
              <td>
                {{ subscriptionPackageId[0].subscriptionPackage.usage }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        v-else-if="
          subscriptionDataForApi.type === 'file' &&
          currentBillingComp === 'file'
        "
      >
        <div class="border-2 border-black-600 px-4 lg:px-[40px] py-[20px]">
          <div class="hidden">
            <p class="text-base md:text-[20px] font-semibold mb-[20px]">
              {{ $t("order_id") }} : TMS21696843
            </p>
          </div>
          <div class="flex justify-between text-[20px] my-[20px]">
            <p>{{ subscriptionDataForApi.downloadFileDetails.title }}</p>
            <p>৳ {{ subscriptionDataForApi.downloadFileDetails.price }}</p>
          </div>
          <div class="flex justify-between text-[20px]">
            <p>{{ $t("discount_amount") }}</p>
            <p class="text-primary-red">
              ৳ {{ subscriptionDataForApi.downloadFileDetails.special_price }}
            </p>
          </div>

          <div
            class="flex justify-between text-[20px] my-[20px] border-t pt-4 border-black"
          >
            <p>{{ $t("total_price") }}</p>
            <p class="text-primary-red">
              ৳ {{ subscriptionDataForApi.downloadFileDetails.special_price }}
            </p>
          </div>

          <div class="flex justify-between mt-[50px] items-cente_r">
            <p class="text-base md:text-[20px] font-semibold">
              {{ $t("select_payment_method") }}
            </p>
            <div
              class="flex items-center border px-[16px] py-[8px] rounded-3xl"
            >
              <div class="p-3 bg-primary-red rounded-3xl">
                <img
                  class="w-3 h-[14px]"
                  src="/images/billing-methods/Layer_1.png"
                  alt=""
                />
              </div>
              <p class="text-sm sm:text-[16px] ml-5">
                {{ $t("completely_secured") }}
              </p>
            </div>
          </div>
          <div
            class="flex justify-between border border-black rounded-xl mt-[50px] py-3 px-6"
          >
            <div class="flex items-center">
              <input
                v-model="methodAccepted"
                type="radio"
                id="radioButton"
                name="radioButton"
                :value="true"
                class="h-5 w-5 text-blue-500 focus:ring-blue-400 border-gray-300 rounded-md"
                required
              />
              <label for="radioButton" class="ml-5 text-gray-700">{{
                $t("payment_method")
              }}</label>
            </div>
            <img
              class="w-20 aspect-video"
              src="/images/billing-methods/payment.svg"
            />
          </div>
          <div class="flex items-start gap-4 pt-5">
            <input
              type="checkbox"
              v-model="termsAccepted"
              class="w-6 h-6 aspect-square"
            />
            <p class="font-semibold">
              {{ $t("billing_policy_text") }}
              <NuxtLink
                :to="localePath('/privacy-policy')"
                target="_blank"
                class="text-primary-red"
              >
                {{ $t("privacy_policy") }} </NuxtLink
              >,
              <NuxtLink
                :to="localePath('/terms-of-use')"
                target="_blank"
                class="text-primary-red"
              >
                {{ $t("terms_conditions") }} </NuxtLink
              >,
              <NuxtLink
                :to="localePath('/refund-policy')"
                target="_blank"
                class="text-primary-red"
              >
                {{ $t("refund_policy") }}
              </NuxtLink>
              {{ $t("and") }}
              <NuxtLink
                :to="localePath('/cancellation-policy')"
                target="_blank"
                class="text-primary-red"
              >
                {{ $t("cancellation_policy") }} </NuxtLink
              >.
            </p>
          </div>
          <div class="flex p-5 w-full">
            <button
              v-if="!isManualPayment"
              class="outline-none text-white font-semibold py-2 md:py-3 rounded-sm w-full"
              :class="
                (allCartData.length === 0 &&
                  subscriptionPackageId.length === 0 &&
                  subscriptionDataForApi.type !== 'file') ||
                isOrderBtnDisabled ||
                !termsAccepted ||
                !methodAccepted
                  ? 'bg-red-300'
                  : 'bg-primary-red'
              "
              type="button"
              :disabled="
                (allCartData.length === 0 &&
                  subscriptionPackageId.length === 0 &&
                  subscriptionDataForApi.type !== 'file') ||
                isOrderBtnDisabled ||
                !termsAccepted ||
                !methodAccepted
              "
              @click="placeOrder"
            >
              {{ $t("place_order_btn") }}
            </button>
            <button
              v-else
              class="outline-none text-white font-semibold py-2 md:py-3 rounded-sm w-full"
              :class="
                (allCartData.length === 0 &&
                  subscriptionPackageId.length === 0 &&
                  subscriptionDataForApi.type !== 'file') ||
                isOrderBtnDisabled ||
                !termsAccepted ||
                !methodAccepted
                  ? 'bg-red-300'
                  : 'bg-primary-red'
              "
              type="button"
              :disabled="
                (allCartData.length === 0 &&
                  subscriptionPackageId.length === 0 &&
                  subscriptionDataForApi.type !== 'file') ||
                isOrderBtnDisabled ||
                !termsAccepted ||
                !methodAccepted
              "
              @click="handleManualPayment"
            >
              {{ $t("Next") }}
            </button>
          </div>
        </div>

        <FileDetailModal
          v-if="showFileModal && false"
          :fileDetail="subscriptionDataForApi.downloadFileDetails?.description"
          :showModal="showFileModal"
          @closeModal="showFileModal = false"
        />
      </div>
    </div>

    <div
      v-else
      class="w-full h-[50vh] flex flex-col justify-center items-center"
    >
      <IconsIsLoading />
      <p class="text-lg pt-20">{{ $t("please_wait") }}</p>
    </div>
  </div>
</template>
<style scoped>
th,
td {
  @apply text-left min-w-[80px] text-base lg:text-lg border border-black py-2 lg:py-3 px-5;
}

input[type="radio"] {
  accent-color: #ec1f27;
}
</style>