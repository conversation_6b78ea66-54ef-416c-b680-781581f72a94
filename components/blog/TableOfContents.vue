<script setup lang="ts">
import type {
  ProcessedContentData,
  TableOfContentsItem,
} from "~/composables/useTableOfContents";

interface Props {
  content: string;
  title?: string;
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: "Table of Contents",
  class: "",
});

// Use the table of contents composable
const { processHeadingsData, scrollToHeading, setupIntersectionObserver } =
  useTableOfContents();

// Table of contents functionality
const tableOfContents = ref<TableOfContentsItem[]>([]);
const activeHeadingId = ref<string>("");

// Cache for processed data to ensure consistency
const processedData = ref<ProcessedContentData>({
  toc: [],
  processedContent: "",
});

// Update processed data when content changes
const updateProcessedData = () => {
  const data = processHeadingsData(props.content);
  processedData.value = data;
  tableOfContents.value = data.toc;
};

// Process content from cached data
const processedContent = computed(() => {
  return processedData.value.processedContent;
});

// Generate TOC when content is available (works on both server and client)
watch(
  () => props.content,
  (newContent) => {
    if (newContent) {
      updateProcessedData();
    }
  },
  { immediate: true }
);

// Setup intersection observer on mount
onMounted(() => {
  if (props.content) {
    updateProcessedData();

    // Setup intersection observer after content is rendered
    nextTick(() => {
      setupIntersectionObserver(activeHeadingId);

      // Set initial active heading (first heading if none is active)
      if (!activeHeadingId.value && tableOfContents.value.length > 0) {
        activeHeadingId.value = tableOfContents.value[0].id;
      }
    });
  }
});

// Expose processed content and table of contents for parent component
defineExpose({
  processedContent,
  tableOfContents,
  activeHeadingId,
  scrollToHeading,
});
</script>

<template>
  <div :class="['table-of-contents-wrapper', props.class]">
    <div class="flex flex-col pb-8">
      <p class="text-2xl font-bold text-black leading-8">
        {{ $t(title) }}
      </p>
      <div class="py-8 transition-all duration-300">
        <nav v-if="tableOfContents.length > 0">
          <ul class="space-y-2">
            <li
              v-for="(item, index) in tableOfContents"
              :key="item.id"
              :class="[
                'text-base font-medium leading-[21px] transition-all duration-200',
                item.level === 1 && index !== 0 ? 'ml-0 pt-4' : '',
                item.level === 2 && index !== 0 ? 'ml-0 pt-4' : '',
                item.level === 3 && index !== 0 ? 'ml-4' : '',
                item.level === 4 && index !== 0 ? 'ml-4' : '',
                item.level === 5 && index !== 0 ? 'ml-4' : '',
                item.level === 6 && index !== 0 ? 'ml-4' : '',
              ]"
            >
              <button
                @click="scrollToHeading(item.id)"
                :class="[
                  'text-left transition-colors duration-200 cursor-pointer block w-full pr-2',
                  activeHeadingId === item.id
                    ? 'text-primary-red'
                    : 'text-black hover:text-primary-red',
                ]"
                :title="item.text"
              >
                {{ item.text }}
              </button>
            </li>
          </ul>
        </nav>
        <div v-else class="text-sm text-gray-500 italic">
          {{ $t("No_headings_found_in_this_article") }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Component-specific styles can be added here if needed */
</style>
