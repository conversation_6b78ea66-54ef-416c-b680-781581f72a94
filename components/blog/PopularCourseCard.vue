<script setup>
import courseThumbnail from "~/assets/img/default/course-thumbnail.png";

defineProps({
  card: {
    type: Object,
    required: true,
  },
});

const localePath = useLocalePath();
</script>

<template>
  <div class="card w-full h-full flex flex-col">
    <img
      :src="card?.banner_url ? card.banner_url : courseThumbnail"
      :alt="card?.title"
      width="305"
      height="172"
      loading="lazy"
      class="object-cover w-full h-auto aspect-[305/172] rounded-[10px]"
    />

    <div class="p-4 pb-3 flex flex-col flex-grow">
      <div class="text-start flex-grow">
        <h2 class="text-xl font-semibold text-black line-clamp-1 mb-3">
          {{ card?.title }}
        </h2>
        <p class="text-sm line-clamp-2 mb-4">
          {{ card?.subtitle }}
        </p>
      </div>
      <NuxtLink
        :to="localePath(`/precheckout/${card?.slug}`)"
        class="text-[#EC1F27] text-base font-semibold bg-[#FFEEEE] w-full h-[37px] rounded-[10px] text-center leading-9"
      >
        {{ $t("enroll_now") }}
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped>
.card {
  @apply bg-white rounded-2xl text-black;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
}
/* .card:hover {
  box-shadow: 0px 20px 40px 0px rgba(183, 188, 201, 0.25);
} */
</style>
