<script setup lang="ts">
import type { BlogDetails } from "~/types/blog";

defineProps<{
  post: BlogDetails | null;
}>();
</script>

<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
    <div class="flex flex-col justify-center text-black">
      <p class="text-sm leading-4">{{ post?.reading_time_minutes }}</p>
      <h1
        class="text-[52px] font-bold leading-[69px] font-noto-sans-bengali mt-3 min-h-[200px] pb-5"
      >
        <!-- মোবাইল সার্ভিসিং কেন এই যুগের সেরা ক্যারিয়ার -->
        {{ post?.title }}
      </h1>
      <div
        class="flex items-center gap-2 text-base leading-[19px] text-[#787878] mt-2"
      >
        <span>SK Mobile School</span>
        <span>|</span>
        <p class="flex items-center gap-2">
          <IconsCalender class="text-[#EC1F27] size-3.5" />
          <span>{{
            $dateFormat(post?.created_at || new Date().toDateString())
          }}</span>
        </p>
        <span>|</span>
        <span>By {{ post?.created_by?.name }}</span>
      </div>
    </div>
    <div class="size-full flex justify-center items-center">
      <img
        v-if="post?.image"
        :src="post.image"
        alt="featured.title"
        width="1260"
        height="790"
        loading="lazy"
        class="w-full h-auto max-h-[395px] md:min-h-[330px] aspect-[380/214] md:aspect-[1260/790] object-cover rounded-2xl"
      />
    </div>
  </div>
</template>

<style scoped></style>
