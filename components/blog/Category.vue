<script setup lang="ts">
import type { BlogCategory } from "~/types/blog";

defineProps<{
  categories: BlogCategory[];
}>();

const route = useRoute();
const category = computed(() => route.params?.category);

const localePath = useLocalePath();
const scrollContainer = ref<HTMLElement | null>(null);

const scrollToActiveLink = () => {
  nextTick(() => {
    if (!scrollContainer.value) return;

    const activeLink = scrollContainer.value.querySelector(".active-link");
    if (activeLink) {
      activeLink.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
        inline: "center",
      });
    }
  });
};

watch(category, () => {
  scrollToActiveLink();
});

onMounted(() => {
  scrollToActiveLink();
});
</script>

<template>
  <div class="">
    <h1 v-if="category" class="font-bold text-[32px] leading-[38px] text-black">
      {{ $t("browse_by_category") }}
    </h1>
    <h2 v-else class="font-bold text-[32px] leading-[38px] text-black">
      {{ $t("browse_by_category") }}
    </h2>
    <div
      ref="scrollContainer"
      class="flex items-center scroll gap-4 w-full overflow-x-auto pt-6 pb-0.5"
    >
      <NuxtLink
        :to="localePath(`/blog`)"
        exact-active-class="active-link"
        class="category-link"
      >
        All Categories
      </NuxtLink>

      <NuxtLink
        v-for="category in categories"
        :key="category.id"
        :to="localePath(`/blog/category/${category.slug}`)"
        exact-active-class="active-link"
        class="category-link first-letter:uppercase"
      >
        {{ category.title }}
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped>
.scroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  scrollbar-width: thin;
  @apply hidden md:block;
}

.category-link {
  @apply whitespace-nowrap px-4 rounded-[13px] text-sm leading-[30px] font-bold border border-[#EC1F26] bg-white text-[#464646] hover:text-white hover:bg-[#EC1F26];
}

.active-link {
  @apply bg-[#EC1F26] text-white;
}
</style>
