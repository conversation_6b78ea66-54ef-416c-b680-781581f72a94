<script setup lang="ts">
import type { BlogPost } from "~/types/blog";

defineProps<{
  post: BlogPost;
}>();

const localePath = useLocalePath();
</script>
<template>
  <div
    class="bg-[#F8F8F8] p-4 rounded-2xl shadow-[2px_2px_4px_0px_#0000001A,_-2px_-2px_4px_0px_#0000001A]"
  >
    <NuxtLink :to="localePath(`/blog/${post.slug}`)">
      <img
        :src="post.image"
        :alt="post.title"
        width="762"
        height="428"
        loading="lazy"
        class="w-full h-auto min-h-[200px] md:min-h-[214px] aspect-[762/428] object-cover rounded-[18px]"
      />
    </NuxtLink>
    <div class="flex flex-col pt-4">
      <div
        class="flex items-center gap-2 text-sm font-medium leading-4 text-[#787878]"
      >
        <span>{{ post.createdByUserName }}</span>
        <span>|</span>
        <p class="flex items-center gap-2">
          <IconsCalender class="text-[#EC1F27] size-3.5" />
          <span>{{ $dateFormat(post.created_at) }}</span>
        </p>
      </div>
      <NuxtLink
        :to="localePath(`/blog/${post.slug}`)"
        class="text-xl text-black font-bold hover:text-[#EC1F26] line-clamp-2 mt-2.5"
      >
        <h2>{{ post.title }}</h2>
      </NuxtLink>
      <p
        v-if="post?.short_description"
        class="text-base text-[#00000099] line-clamp-2 mt-2"
      >
        {{ post.short_description.replace(/\s+/g, " ").trim() }}
      </p>
    </div>
  </div>
</template>

<style scoped></style>
