<script setup lang="ts">
import type { BlogPost } from "~/types/blog";

defineProps<{
  featured: BlogPost | null;
}>();

const localePath = useLocalePath();
</script>

<template>
  <div
    v-if="featured"
    class="w-full p-4 rounded-2xl grid grid-cols-1 md:grid-cols-2 shadow-[2px_2px_4px_0px_#0000001A,_-2px_-2px_4px_0px_#0000001A] mt-6"
  >
    <div class="size-full flex justify-center items-center">
      <img
        :src="featured.image"
        :alt="featured.title"
        width="1260"
        height="790"
        loading="lazy"
        class="w-full h-auto max-h-[395px] md:min-h-[330px] aspect-[380/214] md:aspect-[1260/790] object-cover rounded-2xl"
      />
    </div>
    <div class="p-5 xl:pl-8 xl:py-9 flex flex-col">
      <div
        class="flex items-center gap-2 text-base leading-[19px] text-[#787878]"
      >
        <span>{{ featured.createdByUserName }}</span>
        <span>|</span>
        <p class="flex items-center gap-2">
          <IconsCalender class="text-[#EC1F27] size-3.5" />
          <span>{{ $dateFormat(featured.created_at) }}</span>
        </p>
      </div>
      <div class="flex flex-col flex-grow pt-6 pb-4">
        <h2
          class="text-[32px] leading-[42px] font-bold line-clamp-2 text-black"
        >
          {{ featured.title }}
        </h2>
        <p
          class="text-base md:text-xl leading-[27px] text-black line-clamp-3 xl:line-clamp-4 pt-4"
        >
          {{ featured.short_description.replace(/\s+/g, " ").trim() }}
        </p>
      </div>
      <NuxtLink
        :to="localePath(`/blog/${featured?.slug}`)"
        class="text-base font-bold flex items-center justify-center gap-1 bg-[#EC1F26] text-white rounded-[18px] px-6 py-3 w-min"
      >
        <span class="whitespace-nowrap">{{ $t("continue_reading") }}</span>
        <IconsNorthArrow />
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped></style>
