<script setup lang="ts">
import type { BlogCategory, BlogResponse } from "~/types/blog";

const props = defineProps<{
  category: BlogCategory;
}>();

const config = useRuntimeConfig();
const localePath = useLocalePath();
const { BLOGS } = useUrls();

const { data: blogsRes } = await useFetch<BlogResponse>(
  `${BLOGS}?per_page=3&slug=${props.category.slug}`
);

const blogs = computed(() => blogsRes.value?.data || []);

const siteUrl = computed(() => {
  return `${config.public.siteUrl}`;
});

watchEffect(() => {
  if (blogs.value.length) {
    useSchemaOrg([
      defineItemList({
        "@context": "https://schema.org",
        "@type": "ItemList",
        name: "Blogs by Category - " + props.category.title,
        itemListElement: blogs.value?.map((post, index) => ({
          "@type": "ListItem",
          position: index + 1,
          item: {
            "@context": "https://schema.org",
            "@id": `${siteUrl.value}${localePath(`/blog/${post.slug}`)}`,
            "@type": "BlogPosting",
            name: post.title,
            description: post.short_description,
            url: `${siteUrl.value}${localePath(`/blog/${post.slug}`)}`,
            image: {
              "@type": "ImageObject",
              "@id": post.image + "#BlogPostingImage",
              url: post.image,
            },
            datePublished: post.created_at,
            author: {
              "@type": "Person",
              name: post.createdByUserName || "Sk Mobile School Author",
            },
            publisher: {
              "@type": "Organization",
              "@id": siteUrl.value + "#Organization",
              name: "Sk Mobile School",
              logo: {
                "@type": "ImageObject",
                "@id":
                  siteUrl.value + "/images/logo.webp" + "#OrganizationLogo",
                url: siteUrl.value + "/images/logo.webp",
              },
            },
            isPartOf: {
              "@type": "Blog",
              "@id": siteUrl.value + localePath("/blog") + "#Blog",
              name: "Sk Mobile School Blog",
              publisher: {
                "@type": "Organization",
                "@id": siteUrl.value + "#Organization",
                name: "Sk Mobile School",
              },
            },
          },
        })),
      }),
    ]);
  }
});
</script>
<template>
  <div v-if="blogs.length" class="w-full">
    <div class="flex items-center justify-between">
      <h2
        class="text-[32px] font-bold leading-[38px] text-black first-letter:uppercase"
      >
        {{ category.title }}
      </h2>
      <NuxtLink
        :to="localePath(`/blog/category/${category.slug}`)"
        class="flex items-center gap-1 text-[#EC1F26] text-sm font-medium hover:bg-[#EC1F26]/5 px-2 py-1.5 rounded-lg"
      >
        <span class="uppercase">{{ $t("see_more") }}</span>
        <icons-chevron-down class="size-3.5 -rotate-90" />
      </NuxtLink>
    </div>
    <div class="pt-8 md:grid grid-cols-2 lg:grid-cols-3 gap-5 hidden">
      <BlogCard
        v-for="blog in blogs"
        :key="blog.id"
        :post="blog"
        :class="[blogs.length === 3 ? 'last:hidden lg:last:block' : '']"
      />
    </div>
  </div>
</template>

<style scoped></style>
