<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";

const emit = defineEmits<{
  (e: "goToPage", page: number): void;
  (e: "goToPrevPage"): void;
  (e: "goToNextPage"): void;
}>();

const props = defineProps<{
  currentPage: number;
  totalPages: number;
}>();

const breakpoints = useBreakpoints(breakpointsTailwind);

const isPrevBtnDisabled = computed(() => {
  return props.currentPage <= 1;
});
const isNextBtnDisabled = computed(() => {
  return props.currentPage >= props.totalPages;
});

const isSmallMobile = breakpoints.smaller("sm");
const isTablet = breakpoints.between("sm", "lg");
const isDesktop = breakpoints.greaterOrEqual("lg");

const perPagePaginationButton = computed(() => {
  if (isSmallMobile.value) {
    return 4;
  } else if (isTablet.value) {
    return 8;
  } else if (isDesktop.value) {
    return 12;
  }
  return 4;
});

const visiblePageNumbers = computed(() => {
  const startPage = Math.max(
    1,
    props.currentPage - Math.floor(perPagePaginationButton.value / 2)
  );
  const endPage = Math.min(
    props.totalPages,
    startPage + perPagePaginationButton.value - 1
  );

  return Array.from(
    { length: endPage - startPage + 1 },
    (_, index) => startPage + index
  );
});

const isCurrentPage = (page: number) => {
  return props.currentPage === page;
};
</script>

<template>
  <div class="flex justify-center items-center gap-1">
    <button
      v-if="!isPrevBtnDisabled"
      type="button"
      :disabled="isPrevBtnDisabled"
      class="pagination-btn inactive-btn px-2 gap-1 hidden sm:block"
      :class="{ 'opacity-50': isPrevBtnDisabled }"
      @click="emit('goToPrevPage')"
    >
      <IconsChevronDown class="size-3 rotate-90" />
      <span>Prev</span>
    </button>
    <button
      v-for="(page, index) in visiblePageNumbers"
      :key="index"
      type="button"
      class="pagination-btn"
      :class="[isCurrentPage(page) ? 'active-btn' : 'inactive-btn']"
      @click="emit('goToPage', page)"
    >
      <span>{{ page }}</span>
    </button>
    <button
      v-if="!isNextBtnDisabled"
      type="button"
      :disabled="isNextBtnDisabled"
      class="pagination-btn inactive-btn px-2 gap-1 hidden sm:block"
      :class="{ 'opacity-50': isNextBtnDisabled }"
      @click="emit('goToNextPage')"
    >
      <span>Next</span>
      <IconsChevronDown class="size-3 -rotate-90" />
    </button>
  </div>
</template>

<style scoped>
.pagination-btn {
  @apply min-w-9 h-9 rounded-lg border text-black text-sm font-semibold flex items-center justify-center hover:border-[#E5E5E5] transition-all duration-300;
}
.active-btn {
  @apply border-[#E5E5E5];
}
.inactive-btn {
  @apply border-transparent;
}
</style>
