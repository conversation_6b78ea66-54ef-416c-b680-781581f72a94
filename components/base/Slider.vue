<script setup lang="ts">
import { Carousel, Navigation, Pagination, Slide } from "vue3-carousel";
import "vue3-carousel/dist/carousel.css";

const props = defineProps({
  sliders: {
    type: Array,
    required: true,
  },
  isSingleSlider: {
    type: Boolean,
    default: false,
  },
  showPagination: {
    type: Boolean,
    default: true,
  },
  showNavigation: {
    type: Boolean,
    default: true,
  },
  activeItemFocused: {
    type: Boolean,
    default: false,
  },
  carouselClass: {
    type: String,
    default: "",
  },
  carouselItemClass: {
    type: String,
    default: "p-4",
  },
  navigationClass: {
    type: String,
    default: "navigation",
  },
  paginationClass: {
    type: String,
    default: "pagination",
  },
  wrapAround: {
    type: Boolean,
    default: false,
  },
});

const { activeItemFocused, wrapAround } = toRefs(props);

const settings: any = {
  itemsToShow: 1,
  snapAlign: "center",
  // modelValue: props.slider.length === 3 ? 1 : 0,
  autoplay: 5000,
  wrapAround: wrapAround.value,
  transition: 500,
  pauseAutoplayOnHover: true,
};

const breakpoints = {
  640: {
    itemsToShow: 2,
    snapAlign: "start",
  },
  1200: {
    itemsToShow: 3,
    snapAlign: "center",
  },
};

const sliderCss = computed(() => {
  const defaultCss = {
    opasity: "1",
    perspective: "none",
    preserve3d: "flat",
    rotateY1: "none",
    rotateY2: "none",
    rotateY3: "none",
  };
  if (activeItemFocused.value) {
    defaultCss.opasity = "1";
    defaultCss.perspective = "2000px";
    defaultCss.preserve3d = "preserve-3d";
    defaultCss.rotateY1 = "rotateY(-20deg) scale(0.9)";
    defaultCss.rotateY2 = "rotateY(20deg) scale(0.9)";
    defaultCss.rotateY3 = "rotateY(0) scale(1.1)";
  }
  return defaultCss;
});
</script>

<template>
  <Carousel
    v-bind="settings"
    :breakpoints="!isSingleSlider ? breakpoints : {}"
    :class="carouselClass"
  >
    <Slide v-for="(slider, index) in sliders" :key="(slider as any).id">
      <div class="carousel__item" :class="carouselItemClass">
        <slot name="slider" :data="slider as any" :index="index"></slot>
      </div>
    </Slide>

    <template #addons>
      <Navigation v-if="showNavigation" :class="navigationClass" />
      <Pagination v-if="showPagination" :class="paginationClass" />
    </template>
  </Carousel>
</template>

<style scoped>
.carousel__item {
  @apply w-full h-full;
}

.carousel__viewport {
  perspective: v-bind("sliderCss.perspective");
}

.carousel__track {
  transform-style: v-bind("sliderCss.preserve3d");
}

.carousel__slide--sliding {
  transition: all 0.5s ease-in-out;
}

.carousel__slide {
  opacity: v-bind("sliderCss.opasity");
  transform: v-bind("sliderCss.rotateY1");
}

.carousel__slide--active ~ .carousel__slide {
  transform: v-bind("sliderCss.rotateY2");
}

.carousel__slide--active {
  opacity: 1;
  transform: v-bind("sliderCss.rotateY3");
}
</style>
