<script setup>
import { useIndexStore } from "~/stores/index";

const props = defineProps({
  card: {
    type: Object,
    required: true,
  },
  openInModal: {
    type: Boolean,
    default: false,
  },
  showDescription: {
    type: Boolean,
    default: false,
  },
  cardClass: {
    type: String,
    default: "w-full h-full",
  },
  mediaClass: {
    type: String,
    default: "w-full aspect-video rounded-[20px]",
  },
  headingClass: {
    type: String,
    default: "text-xl md:text-2xl lg:text-4xl",
  },
  subHeadingClass: {
    type: String,
    default: "text-sm md:text-base xl:text-xl",
  },
});

const { card, openInModal } = toRefs(props);
const { setMedia } = useIndexStore();

const setModalData = () => {
  setMedia(card.value);
};
</script>

<template>
  <div class="relative" :class="cardClass">
    <NuxtLink v-if="card.type === 'image'" :to="card.url ? card.url : '#'">
      <img
        class="object-fill object-top"
        :class="mediaClass"
        :src="card.media_link"
        :alt="card.title"
      />
    </NuxtLink>
    <BaseThumbWithPlayBtn
      v-else-if="card.type === 'video' && openInModal"
      :coverImage="card.cover_image"
      :altText="card.title"
      :thumbClass="mediaClass"
      :wrapperClass="mediaClass"
      @toggleMedia="setModalData"
    />
    <BaseEmbadedVideoPlayer
      v-else
      playerId="coursePlayer"
      :videoTitle="card.title"
      :videoUrl="card?.media_link ? card.media_link : card?.media_url"
      :coverImage="card.cover_image"
      :videoClass="mediaClass"
    />
    <div
      v-if="showDescription && card.type === 'image'"
      class="z-[1] w-full h-full absolute inset-0 mx-auto text-white flex flex-col justify-between items-center py-4 px-12 md:py-8 xl:p-20"
    >
      <div class="space-y-2.5 break-word text-center max-w-[600px] mx-auto">
        <p class="font-semibold text-2xl" :class="headingClass">
          {{ card.title }}
        </p>
        <p :class="subHeadingClass">{{ card.sub_title }}</p>
      </div>
      <NuxtLink
        v-if="card.url"
        :to="card.url"
        class="red-button bg-transparent text-white text-sm md:text-base lg:text-lg border-2 border-white py-1.5 md:w-44"
      >
        <span>{{ $t("view_course_btn") }}</span>
      </NuxtLink>
    </div>
  </div>
</template>
