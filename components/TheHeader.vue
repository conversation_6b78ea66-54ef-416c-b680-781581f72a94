<script setup lang="ts">
const route = useRoute();
const { isSmaller } = useBreakpoints();
const localePath = useLocalePath();

const isMobile = computed(() => isSmaller(1024));
const isMenuOpen = ref(false);

const iosPayment = computed(() => {
  return Boolean(route.query?.iosToken || route.query?.iosPayment);
});

const toggleDropdown = () => {
  isMenuOpen.value = !isMenuOpen.value;
};
const closeMenu = () => {
  if (isMobile.value) {
    isMenuOpen.value = false;
  }
  return;
};

defineExpose({
  closeMenu,
});

const isActivePage = (
  pages: String[],
  activeClass: String,
  inactiveClass: String
) => {
  const currentRouteName = (route.name as any).slice(0, -5);
  return pages.includes(currentRouteName) ? activeClass : inactiveClass;
};
</script>

<template>
  <section>
    <div class="bg-rgba(255, 255, 255, 1) notranslate">
      <nav
        class="relative flex w-full flex-wrap items-center justify-between text-neutral-500 shadow-lg"
      >
        <div
          class="custom-container flex w-full items-center justify-between flex-col lg:flex-row relative"
        >
          <div class="flex justify-between w-full lg:w-fit">
            <div @click="closeMenu">
              <NuxtLink :to="localePath('/')">
                <img
                  class="py-1 w-[93px] h-[60px] md:h-[65px]"
                  src="/images/sk-new-logo.svg"
                  alt="SK Mobile School logo"
                />
              </NuxtLink>
            </div>

            <button
              v-if="!iosPayment"
              class="block border-0 bg-transparent text-neutral-500 hover:no-underline hover:shadow-none focus:no-underline focus:shadow-none focus:outline-none focus:ring-0 dark:text-neutral-200 lg:hidden"
              type="button"
              @click.stop="toggleDropdown"
            >
              <span class="[&>svg]:w-7">
                <IconsHamburgerMenu />
              </span>
            </button>
          </div>

          <NavMenu
            v-if="!iosPayment"
            class="hidden mt-2 flex-grow basis-[100%] items-center lg:mt-0 lg:flex lg:flex-wrap lg:basis-auto justify-end w-full lg:w-fit"
            :iosPayment="iosPayment"
            :isActivePage="isActivePage"
            :closeMenu="closeMenu"
          />
          <NavMenu
            v-if="isMenuOpen && !iosPayment"
            class="mobile-menu mt-2 flex-grow basis-[100%] items-center lg:hidden justify-end w-full"
            :iosPayment="iosPayment"
            :isActivePage="isActivePage"
            :closeMenu="closeMenu"
          />
        </div>
      </nav>
    </div>
  </section>
</template>

<style scoped>
.dropdown-content {
  @apply text-lg bg-white;
  z-index: 1;
}
.dropdown-content li:hover {
  @apply bg-black text-white font-semibold;
}
@media (max-width: 1023px) {
  .mobile-menu {
    @apply absolute top-[54px] md:top-[60px] right-0 z-20 bg-white shadow-xl pb-2;
  }
}
</style>
