const lastmod = new Date("2025-10-16").toISOString();

const pages = [
  { path: "", lastmod, changefreq: "weekly", priority: 1 },
  { path: "/about-us", lastmod, changefreq: "weekly", priority: 1 },
  { path: "/blog", lastmod, changefreq: "weekly", priority: 1 },
  { path: "/browse-course", lastmod, changefreq: "weekly", priority: 1 },
  { path: "/communication", lastmod, changefreq: "weekly", priority: 1 },
  { path: "/contact-us", lastmod, changefreq: "weekly", priority: 1 },
  { path: "/demo-class", lastmod, changefreq: "weekly", priority: 1 },
  { path: "/firmware", lastmod, changefreq: "weekly", priority: 1 },
  { path: "/online-services", lastmod, changefreq: "weekly", priority: 1 },
  { path: "/success-story", lastmod, changefreq: "weekly", priority: 1 },
];

const fetchCollection = async (apiUrl: string, endpoint: string) => {
  try {
    const res = (await $fetch(`${apiUrl}/${endpoint}`)) as any;
    return res;
  } catch (err) {
    console.error(`Failed to fetch ${endpoint}:`, err);
    return null;
  }
};

const siteMapItem = (pages: any, siteUrl: string) => {
  return pages
    .map(
      (page: any) => `
        <url>
          <loc>${siteUrl}${page.path}</loc>
          <lastmod>${page.lastmod}</lastmod>
          <changefreq>${page.changefreq}</changefreq>
          <priority>${page.priority}</priority>
        </url>
      `
    )
    .join("\n");
};

const sitemapLocale = async ({
  siteUrl,
  apiUrl,
}: {
  siteUrl: string;
  apiUrl: string;
}) => {
  const [blogsRes, coursesRes, categoryRes] = await Promise.all([
    fetchCollection(apiUrl, "blogs?per_page=2000"),
    fetchCollection(apiUrl, "courses?per_page=200"),
    fetchCollection(apiUrl, "blog-categories?per_page=-1"),
  ]);

  const blogs = Array.isArray(blogsRes?.data) ? blogsRes.data : [];
  const courses = Array.isArray(coursesRes?.data) ? coursesRes.data : [];
  const categories = Array.isArray(categoryRes?.data) ? categoryRes.data : [];

  const blogsMap = blogs.map((post: any) => ({
    path: `/blog/${post.slug}`,
    lastmod: post?.updated_at || post?.created_at || lastmod,
    changefreq: "weekly",
    priority: 1,
  }));

  const coursesMap = courses.map((course: any) => ({
    path: `/precheckout/${course.slug}`,
    lastmod: course?.updated_at || course?.created_at || lastmod,
    changefreq: "weekly",
    priority: 1,
  }));

  const categoriesMap = categories.map((category: any) => ({
    path: `/blog/category/${category.slug}`,
    lastmod: category?.updated_at || category?.created_at || lastmod,
    changefreq: "weekly",
    priority: 1,
  }));

  const xml = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
      ${siteMapItem(pages, siteUrl)}
      ${siteMapItem(coursesMap, siteUrl)}
      ${siteMapItem(categoriesMap, siteUrl)}
      ${siteMapItem(blogsMap, siteUrl)}
    </urlset>`;

  return xml;
};

export { fetchCollection, lastmod, pages, siteMapItem, sitemapLocale };
