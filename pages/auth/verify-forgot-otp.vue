<script setup>
import { ErrorMessage, Field, Form } from "vee-validate";

useSeoMeta({
  title: "Verify OTP",
});

const { isRequired } = useValidation();
const { apiBaseUrl } = useUrls();
const config = useRuntimeConfig();
const { $toast } = useNuxtApp();
const router = useRouter();
const route = useRoute();
const localePath = useLocalePath();

const otp = ref("");
const email = computed(() => route.query?.email || "");
const recaptchaRef = ref(null);
const loading = ref(false);

const handleVerifyOtp = async () => {
  loading.value = true;
  $toast("clear");

  if (recaptchaRef.value) {
    const captchaToken = await recaptchaRef.value.execute(
      config.public.recaptchaKey,
      {
        action: "VERIFY_EMAIL",
      }
    );

    if (captchaToken) {
      try {
        const data = await $fetch(
          `${apiBaseUrl}/auth/forgot-password/verify-code`,
          {
            method: "POST",
            body: {
              email: email.value,
              code: otp.value,
              captcha_token: captchaToken,
            },
          }
        );
        $toast("success", {
          message: `${data.message}`,
          className: "toasted-bg-success",
        });
        router.push(
          localePath(
            `/auth/reset-password/${data.remember_token}?email=${email.value}`
          )
        );
      } catch (e) {
        $toast("error", {
          message: `${e.data.message}`,
          className: "toasted-bg-alert",
        });
      } finally {
        loading.value = false;
      }
    }
  }
};

const handleForgetPassword = async () => {
  loading.value = true;
  $toast("clear");

  if (recaptchaRef.value) {
    const captchaToken = await recaptchaRef.value.execute(
      config.public.recaptchaKey,
      {
        action: "PASSWORD_RESET",
      }
    );

    if (captchaToken) {
      try {
        const data = await $fetch(`${apiBaseUrl}/auth/forgot-password`, {
          method: "POST",
          body: {
            email: email.value,
            captcha_token: captchaToken,
          },
        });
        $toast("success", {
          message: `${data.message}`,
          className: "toasted-bg-success",
        });
        router.push(localePath(`/auth/verify-forgot-otp?email=${email.value}`));
      } catch (e) {
        $toast("error", {
          message: `${e.data.message}`,
          className: "toasted-bg-alert",
        });
      } finally {
        loading.value = false;
      }
    }
  }
};

onMounted(() => {
  watchEffect(() => {
    if (window.grecaptcha && window.grecaptcha.enterprise) {
      recaptchaRef.value = window.grecaptcha.enterprise;
    }
  });
});
</script>

<template>
  <main class="custom-container">
    <section class="mt-20 mb-32 flex items-center justify-center">
      <div
        class="card-shadow w-[660px] px-2.5 xs:px-4 py-8 md:py-20 md:px-10 font-semibold text-lg"
      >
        <h1 class="text-2xl font-semibold mb-10">
          {{ $t("verify_otp") }}
        </h1>
        <Form
          @submit="handleVerifyOtp"
          @keypress.enter="handleVerifyOtp"
          v-slot="{ meta }"
          class="space-y-5"
        >
          <Field
            v-model="otp"
            name="otp"
            type="text"
            class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg opacity-50"
            :placeholder="$t('enter_otp')"
            :rules="isRequired"
          />
          <ErrorMessage class="error-message block" name="otp" />
          <div class="flex items-center justify-between mt-20">
            <button
              :disabled="!meta.valid || loading"
              class="px-4 h-[50px] text-primary-red border border-primary-red text-lg font-semibold rounded hover:bg-primary-red hover:text-white disabled:opacity-70 disabled:hover:bg-transparent disabled:hover:text-primary-red"
              type="submit"
            >
              {{ $t("continue") }}
            </button>
            <div
              class="flex flex-row items-center text-sm font-medium space-x-1 text-gray-500"
            >
              <p>{{ $t("otp_not_got") }}</p>
              <button
                @click="handleForgetPassword"
                :disabled="loading"
                class="flex flex-row items-center text-blue-600"
                :class="[loading ? 'opacity-50' : 'cursor-pointer']"
              >
                {{ $t("resend_btn") }}
              </button>
            </div>
          </div>
        </Form>
      </div>
    </section>
  </main>
</template>
