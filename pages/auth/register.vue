<script setup>
import FingerprintJS from "@fingerprintjs/fingerprintjs";
import { ErrorMessage, Field, Form } from "vee-validate";

useSeoMeta({
  title: "Register",
});

const { REGISTER } = useUrls();
const { isRequired, validateEmail, validatePassword, validatePhone } =
  useValidation();
const config = useRuntimeConfig();
const nuxtApp = useNuxtApp();
const router = useRouter();
const localePath = useLocalePath();
const { t } = useI18n();

const firstName = ref("");
const lastName = ref("");
const userEmail = ref("");
const userPass = ref("");
const userConfPass = ref("");
const userPhone = ref("");
const isProcessing = ref(false);
const showPassword = ref(false);
const showConfirmPassword = ref(false);
const termsAccepted = ref(false);
const deviceIdCookie = useCookie("deviceId", { maxAge: 60 * 60 * 24 * 30 });

const deviceId = ref("");

const generateDeviceFingerprint = async () => {
  const fpPromise = FingerprintJS.load();
  const fp = await fpPromise;
  const result = await fp.get();
  deviceId.value = result.visitorId;
};

const recaptchaRef = ref(null);
const handleRegister = async () => {
  isProcessing.value = true;

  if (recaptchaRef.value) {
    const captchaToken = await recaptchaRef.value.execute(
      config.public.recaptchaKey,
      {
        action: "SIGNUP",
      }
    );

    if (captchaToken) {
      if (!termsAccepted.value) {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: t("messages.accept_terms_privacy"),
          className: "toasted-bg-alert",
        });
        return;
      }
      if (userPass.value === userConfPass.value) {
        if (deviceId.value !== "") {
          try {
            const userTimeZone =
              Intl.DateTimeFormat().resolvedOptions().timeZone;
            const data = await $fetch(REGISTER, {
              method: "POST",
              body: {
                first_name: firstName.value,
                last_name: lastName.value,
                email: userEmail.value,
                password: userPass.value,
                phone: userPhone.value,
                captcha_token: captchaToken,
                device_id: deviceId.value,
                timezone: userTimeZone,
              },
            });
            if (data) {
              nuxtApp.$toast("clear");
              nuxtApp.$toast("success", {
                message: data.message,
                className: "toasted-bg-success",
              });
              deviceIdCookie.value = deviceId.value;
              router.push(localePath("/auth/login"));
            }
          } catch (error) {
            nuxtApp.$toast("clear");
            nuxtApp.$toast("error", {
              message: error?.response?._data?.message.email
                ? error?.response?._data?.message.email
                : error?.response?._data?.message.phone,
              className: "toasted-bg-alert",
            });
          } finally {
            isProcessing.value = false;
          }
        } else {
          isProcessing.value = false;
        }
      } else {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: t("messages.password_not_match"),
          className: "toasted-bg-alert",
        });
        isProcessing.value = false;
      }
    } else {
      isProcessing.value = false;
    }
  }
};
onMounted(() => {
  generateDeviceFingerprint();

  // Watch for the recaptcha script to be loaded
  watchEffect(() => {
    if (window.grecaptcha && window.grecaptcha.enterprise) {
      recaptchaRef.value = window.grecaptcha.enterprise;
    }
  });
});
</script>

<template>
  <main class="custom-container">
    <section class="mt-20 mb-32 flex items-center justify-center">
      <div class="card-shadow w-[660px] p-4 pt-8 md:p-10 font-semibold text-lg">
        <h1 class="text-2xl font-semibold mb-10">{{ $t("sign_up") }}</h1>
        <Form
          v-if="!isProcessing"
          @submit="handleRegister"
          v-slot="{ meta }"
          class="space-y-5"
        >
          <div class="flex flex-col md:flex-row gap-5">
            <div>
              <Field
                v-model="firstName"
                name="firstName"
                type="text"
                class="w-full md:w-auto md:basis-1/2 px-6 p-2 border border-black outline-none rounded-[10px] text-lg font-semibold"
                :placeholder="$t('enter_first_name')"
                :rules="isRequired"
              />
              <ErrorMessage class="error-message block" name="firstName" />
            </div>
            <div>
              <Field
                v-model="lastName"
                name="lastName"
                type="text"
                class="w-full md:basis-1/2 px-6 p-2 border border-black outline-none rounded-[10px] text-lg font-semibold"
                :placeholder="$t('enter_last_name')"
                :rules="isRequired"
              />
              <ErrorMessage class="error-message block" name="lastName" />
            </div>
          </div>
          <Field
            v-model="userEmail"
            name="email"
            type="email"
            class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg font-semibold"
            :placeholder="$t('email_address')"
            :rules="validateEmail"
          />
          <ErrorMessage class="error-message" name="email" />

          <Field
            v-model="userPhone"
            name="phone"
            type="phone"
            class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg font-semibold"
            :placeholder="$t('phone_number')"
            :rules="validatePhone"
          />
          <ErrorMessage class="error-message" name="phone" />

          <div class="relative">
            <Field
              v-model="userPass"
              name="password"
              :type="showPassword ? 'text' : 'password'"
              class="w-full px-6 p-2 pr-10 border border-black outline-none rounded-[10px] text-lg font-semibold"
              :placeholder="$t('password')"
              :rules="validatePassword"
            />
            <div
              class="absolute top-0 bottom-0 my-auto right-3 w-5 flex items-center"
            >
              <IconsEyeHide
                v-if="!showPassword"
                @click="showPassword = !showPassword"
                class="text-[#03022932] cursor-pointer"
              />
              <IconsEyeShow
                v-else
                @click="showPassword = !showPassword"
                class="text-[#03022932] cursor-pointer w-[18px]"
              />
            </div>
          </div>
          <ErrorMessage class="error-message" name="password" />
          <div class="relative">
            <Field
              v-model="userConfPass"
              name="confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              class="w-full px-6 p-2 pr-10 border border-black outline-none rounded-[10px] text-lg font-semibold"
              :placeholder="$t('confirm_password')"
            />
            <div
              class="absolute top-0 bottom-0 my-auto right-3 w-5 flex items-center"
            >
              <IconsEyeHide
                v-if="!showConfirmPassword"
                @click="showConfirmPassword = !showConfirmPassword"
                class="text-[#03022932] cursor-pointer"
              />
              <IconsEyeShow
                v-else
                @click="showConfirmPassword = !showConfirmPassword"
                class="text-[#03022932] cursor-pointer w-[18px]"
              />
            </div>
          </div>
          <!-- <ErrorMessage class="error-message" name="confirmPassword" /> -->
          <span
            v-if="userPass != userConfPass && userConfPass"
            class="text-base font-normal text-[#ec1f27] pl-1"
            >{{ $t("messages.password_not_match") }}</span
          >

          <div class="flex items-start gap-3">
            <input
              class="h-7 w-7 min-w-[26px]"
              type="checkbox"
              name="termsAccepted"
              id="termsAccepted"
              v-model="termsAccepted"
            />
            <p class="text-lg text-black font-semibold">
              {{ $t("account_terms") }}
              <NuxtLink
                :to="localePath('/terms-of-use')"
                class="text-primary-red"
                >{{ $t("terms_of_use") }}</NuxtLink
              >
              {{ $t("and_2") }}
              <NuxtLink
                :to="localePath('/privacy-policy')"
                class="text-primary-red"
                >{{ $t("privacy_policy") }}</NuxtLink
              >
              {{ $t("give_permission") }}
            </p>
          </div>
          <button
            class="px-5 h-[50px] text-primary-red border border-primary-red text-lg font-semibold rounded disabled:opacity-70 mt-20"
            type="submit"
            :disabled="!meta.valid || isProcessing"
          >
            {{ $t("create_account") }}
          </button>
        </Form>
        <div
          v-else
          class="isLoadingIcon flex flex-col justify-center items-center h-[200px]"
        >
          <IconsIsLoading />
        </div>
        <div class="flex items-center justify-center mt-20">
          <p>
            {{ $t("already_have_account") }}
            <nuxt-link to="/auth/login" class="text-primary-red"
              >{{ $t("login") }}
            </nuxt-link>
          </p>
        </div>
      </div>
    </section>
  </main>
</template>

<style scoped>
.card-shadow {
  box-shadow: 0px -4px 4px 0px #********, 0px 4px 4px 0px #********;
}
</style>
