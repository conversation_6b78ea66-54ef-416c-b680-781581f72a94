<script setup lang="ts">
import defaultThumb from "~/assets/img/default/video-thumbnail.webp";

useSeoMeta({
  title: "Browse Course",
});
const config = useRuntimeConfig();
const { apiBaseUrl, PAGE_OVERVIEW_SECTION, SLIDER } = useUrls();
const localePath = useLocalePath();

const { data: browseCourseSliderRes, pending: browseCourseSliderPending } =
  await useFetch<any>(`${SLIDER}/browse-course`);
const browseCourseSlider = computed(() => browseCourseSliderRes.value?.data);

const { data: pageOverViewRes, pending: pageOverViewPending } =
  await useFetch<any>(`${PAGE_OVERVIEW_SECTION}/browse-course`);
const pageOverView = computed(() => pageOverViewRes.value?.data);

const courseCurrentPage = ref(1);
const courseTotalPages = ref(1);
const perPageCourses = ref(9);
const currentCourses = ref(<any>[]);
const isLoading = ref(false);
const pageOverViewMedia = ref(false);

const isLastPage = computed(() => {
  return courseCurrentPage.value === courseTotalPages.value;
});

const getCourses = async () => {
  try {
    isLoading.value = true;
    const response = await $fetch<any>(`${apiBaseUrl}/courses`, {
      query: {
        per_page: perPageCourses.value,
        page: courseCurrentPage.value,
      },
    });

    if (response?.data) {
      currentCourses.value.push(...response.data);
      courseTotalPages.value = response.meta.last_page;
    }
  } catch (error) {
    console.error("Error fetching courses:", error);
  } finally {
    isLoading.value = false;
  }
};

await getCourses();

const loadMoreCourse = async () => {
  if (isLastPage.value) {
    currentCourses.value = currentCourses.value.slice(0, perPageCourses.value);
    courseCurrentPage.value = 1;
    window.scrollTo(0, 0);
  } else {
    courseCurrentPage.value++;
    await getCourses();
  }
};

useSchemaOrg([
  defineItemList({
    "@context": "https://schema.org/",
    "@type": "Service",
    name: pageOverView.value?.title,
    description: pageOverView.value?.content,
  }),
  defineItemList({
    "@type": "ItemList",
    name: "Demo Courses Slider",
    itemListElement: browseCourseSlider.value?.map(
      (details: any, index: number) => ({
        "@type": "ListItem",
        position: index + 1,
        item: {
          "@context": "https://schema.org/",
          "@type": "ImageObject",
          name: details?.title,
          description: details?.sub_title,
          image: details?.media_link,
        },
      })
    ),
  }),
  defineItemList({
    "@type": "ItemList",
    name: $t("our_courses_title"),
    itemListElement: currentCourses.value?.map(
      (course: any, index: number) => ({
        "@type": "ListItem",
        position: index + 1,
        item: {
          "@context": "https://schema.org/",
          "@type": "Course",
          name: course.title,
          description: course.subtitle,
          url: `${config.public.siteUrl}${localePath(
            `/precheckout/${course.slug}`
          )}`,
          provider: {
            "@type": "Organization",
            name: "Sk Mobile School",
            sameAs: config.public.siteUrl,
            logo: {
              "@type": "ImageObject",
              url: "https://skmobileschool.com/images/logo.webp",
            },
          },
        },
      })
    ),
  }),
]);

onMounted(async () => {
  await nextTick();
  window.scrollTo(0, 0);
});
</script>

<template>
  <div class="custom-container">
    <section class="pt-10 pb-20">
      <div
        v-if="pageOverViewPending"
        class="w-full h-full flex justify-center items-center"
      >
        <span class="text-xl md:text-3xl">Loading...</span>
      </div>
      <div
        v-if="pageOverView && !pageOverViewPending"
        class="flex flex-col-reverse md:flex-row items-center gap-10 xl:gap-20"
      >
        <div class="w-full md:w-1/2 h-full">
          <p
            v-if="pageOverView.sub_title"
            class="text-sm md:text-base lg:text-lg pb-4"
          >
            {{ pageOverView.sub_title }}
          </p>
          <h1
            v-if="pageOverView.title"
            class="text-primary-red font-bold text-[32px] md:text-[40px] xl:text-[60px] pb-4 mt-0 break-words"
          >
            {{ pageOverView.title }}
          </h1>
          <div
            class="text-base md:text-2xl text-justify"
            v-html="pageOverView.content"
          ></div>
        </div>

        <div v-if="pageOverView.media_link" class="w-full md:w-1/2 h-full">
          <div v-if="pageOverView.type === 'video'" class="bg-[#15151F]">
            <Transition name="page" mode="out-in">
              <div v-if="!pageOverViewMedia" class="relative">
                <img
                  class="w-full aspect-video z-[4]"
                  :src="
                    pageOverView?.cover_image
                      ? pageOverView?.cover_image
                      : defaultThumb
                  "
                  alt=""
                />
                <div
                  class="absolute top-0 inset-0 m-auto z-[5] flex justify-center items-center"
                >
                  <div
                    @click="pageOverViewMedia = true"
                    class="cursor-pointer flex justify-center items-center animate-button w-14 aspect-square rounded-full pl-1 text-white"
                  >
                    <IconsPlay class="w-[18px] aspect-[3/4]" />
                  </div>
                </div>
              </div>
              <iframe
                v-else
                class="w-full aspect-video"
                :src="`${pageOverView.media_link}?rel=0&autoplay=1`"
                frameborder="0"
                allowfullscreen
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              ></iframe>
            </Transition>
          </div>
          <img
            v-else
            :src="pageOverView.media_link"
            class="object-cover w-full aspect-video"
            :alt="pageOverView.title"
          />
        </div>
      </div>
      <NoPageFound v-else minHeight="420" />
    </section>

    <section class="pt-5 pb-20">
      <ClientOnly>
        <BaseSlider
          v-if="
            browseCourseSlider &&
            browseCourseSlider.length > 0 &&
            !browseCourseSliderPending
          "
          :sliders="browseCourseSlider"
          isSingleSlider
          showPagination
          wrapAround
          :showNavigation="false"
          carouselItemClass="p-2 rounded-[20px] lg:rounded-[48px]"
        >
          <template #slider="{ data }">
            <div
              class="w-full h-full shadow-[2px_2px_4px_0_#0000001A,-2px_-2px_4px_0_#0000001A] rounded-[20px] lg:rounded-[48px] p-3"
            >
              <CardBrowseCourseSlider
                :card="data"
                :showDescription="false"
                :openInModal="true"
                mediaClass="w-full aspect-video md:aspect-[1280/640] rounded-[20px] lg:rounded-[48px]"
              />
            </div>
          </template>
        </BaseSlider>

        <template #fallback>
          <div
            v-if="
              browseCourseSlider &&
              browseCourseSlider.length > 0 &&
              !browseCourseSliderPending
            "
            class="w-full h-full shadow-[2px_2px_4px_0_#0000001A,-2px_-2px_4px_0_#0000001A] rounded-[20px] lg:rounded-[48px] p-3"
          >
            <CardBrowseCourseSlider
              :card="browseCourseSlider[0]"
              :showDescription="false"
              :openInModal="true"
              mediaClass="w-full aspect-video md:aspect-[1280/640] rounded-[20px] lg:rounded-[48px]"
            />
          </div>
          <div v-else class="w-full h-full flex justify-center items-center">
            <span class="text-xl md:text-3xl">Loading...</span>
          </div>
        </template>
      </ClientOnly>
    </section>

    <section class="flex flex-col pt-5 pb-20 items-center justify-center">
      <div
        v-if="currentCourses.length > 0"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-5 gap-y-6"
      >
        <div
          class="w-full max-w-[410px]"
          v-for="course in currentCourses"
          :key="course.id"
        >
          <img
            v-if="course.banner_url && course.banner_url !== 'null'"
            class="w-full h-[286px] aspect-auto"
            :src="course.banner_url"
            alt="course.title"
          />
          <img
            v-else
            class="w-full h-[286px] aspect-auto"
            src="/images/course-details/courseIcon.png"
            alt="course.title"
          />
          <div class="w-full text-center space-y-3">
            <h3 class="text-2xl font-semibold text-center my-4 line-clamp-2">
              {{ course.title }}
            </h3>
            <NuxtLink
              class="px-4 py-1.5 border border-black text-[#EC1F27] rounded-full"
              :to="localePath(`/precheckout/${course.slug}`)"
            >
              {{ $t("view_course") }}
            </NuxtLink>
          </div>
        </div>
      </div>
      <NoPageFound
        v-else-if="!isLoading && currentCourses.length === 0"
        minHeight="220"
      />
      <div
        v-if="isLoading"
        class="w-full h-full flex justify-center items-center pt-10"
      >
        <span class="text-xl md:text-3xl">Loading...</span>
      </div>
      <div
        v-if="courseTotalPages > 1"
        class="flex items-center justify-center mt-20 mb-10"
      >
        <button
          class="inline-block rounded-full hover:text-white hover:border-primary-red hover:bg-primary-red text-primary-red opacity-100 px-5 py-2 text-base font-bold leading-normal transition-all duration-150 ease-in-out border border-black"
          @click="loadMoreCourse"
        >
          {{ !isLastPage ? $t("load_more") : $t("view_less") }}
        </button>
      </div>
    </section>

    <section class="pt-5 pb-20 flex items-center justify-center">
      <div class="flex flex-col items-center space-y-3 w-[700px]">
        <h4
          class="text-[32px] md:text-[40px] xl:text-6xl font-bold text-center text-[#EC1F27]"
        >
          {{ $t("comments") }}
        </h4>
        <p class="text-sm md:text-xl xl:text-2xl font-normal text-center">
          {{ $t("comment_text") }}
        </p>
      </div>
    </section>
  </div>
</template>

<style scoped>
.card-shadow {
  box-shadow: 0px -4px 4px 0px #00000040;
}

.provider-card-top-shadow {
  box-shadow: 0px -4px 4px 0px #00000040, 0px 4px 4px 0px #00000040;
}
</style>
