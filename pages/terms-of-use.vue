<script setup>
useSeoMeta({
  title: "Terms of Use",
});

onMounted(async () => {
  await nextTick();
  window.scrollTo(0, 0);
});
</script>
<template>
  <main class="custom-container pt-16 pb-20">
    <h1
      class="text-3xl md:text-4xl xl:text-5xl 2xl:text-6xl text-primary-red font-bold pb-10"
    >
      {{ $t("terms.title") }}
    </h1>
    <section class="text-base md:text-2xl space-y-8">
      <p>
        <span class="font-semibold notranslate"> SK Mobile School </span>
        {{ $t("terms.intro_1") }}
        <span class="font-semibold notranslate"> SK Mobile School </span>
        {{ $t("terms.intro_2") }}
      </p>
      <div class="space-y-8">
        <div class="space-y-4">
          <p class="text-primary-red font-semibold">
            {{ $t("terms.section_title_1") }}
          </p>
          <p>
            {{ $t("terms.section_text_1_1") }}
            <span class="font-semibold notranslate"> SK Mobile School </span>
            {{ $t("terms.section_text_1_2") }}
            <span class="font-semibold notranslate"> SK Mobile School </span>
            {{ $t("terms.section_text_1_3") }}
          </p>
        </div>
        <div class="space-y-4">
          <p class="text-primary-red font-semibold">
            {{ $t("terms.section_title_2") }}
          </p>
          <p>
            {{ $t("you") }}
            <span class="font-semibold notranslate"> SK Mobile School </span>
            {{ $t("terms.section_text_2_1") }}
            <span class="font-semibold notranslate"> SK Mobile School </span>
            {{ $t("terms.section_text_2_2") }}
          </p>
          <div class="space-y-4">
            <p>
              {{ $t("terms.section_text_2_3") }}
            </p>
            <p>
              {{ $t("terms.section_text_2_4") }}
            </p>
            <p>
              {{ $t("terms.section_text_2_5") }}
            </p>
            <p>
              {{ $t("terms.section_text_2_6") }}
            </p>
            <p>
              {{ $t("terms.section_text_2_7") }}
            </p>
            <p>
              {{ $t("terms.section_text_2_8") }}
            </p>
          </div>
        </div>
        <div class="space-y-4">
          <p class="text-primary-red font-semibold">
            {{ $t("terms.section_title_3") }}
          </p>
          <p>
            {{ $t("terms.section_text_3_1") }}
            <span class="font-semibold notranslate"> SK Mobile School </span>
            {{ $t("terms.section_text_3_2") }}
          </p>
        </div>
        <div class="space-y-4">
          <p class="text-primary-red font-semibold">
            {{ $t("terms.section_title_4") }}
          </p>
          <p>
            {{ $t("terms.section_text_4_1") }}
            <span class="font-semibold notranslate"> SK Mobile School </span>
            {{ $t("terms.section_text_4_2") }}
          </p>
        </div>
        <div class="space-y-4">
          <p class="text-primary-red font-semibold">
            {{ $t("terms.section_title_5") }}
          </p>
          <p>
            {{ $t("terms.section_text_5") }}
          </p>
        </div>
        <div class="space-y-4">
          <p class="text-primary-red font-semibold">
            {{ $t("terms.section_title_6") }}
          </p>
          <p>
            {{ $t("terms.section_text_6") }}
          </p>
        </div>
        <div class="space-y-4">
          <p class="text-primary-red font-semibold">
            {{ $t("terms.section_title_7") }}
          </p>
          <p>
            {{ $t("terms.section_text_7") }}
          </p>
        </div>
      </div>
    </section>
  </main>
</template>

<style scoped></style>
