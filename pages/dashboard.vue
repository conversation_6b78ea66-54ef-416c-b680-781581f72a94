<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useDashboardStore } from "~/stores/dashboard";
import IconsDashboard from "~/components/icons/Dashboard.vue";
import IconsProfile from "~/components/icons/Profile.vue";
import IconsEnrolledCourses from "~/components/icons/EnrolledCourses.vue";
import IconsOrderHistory from "~/components/icons/OrderHistory.vue";
import IconsQuestionAnswer from "~/components/icons/QuestionAnswer.vue";
import IconsReviews from "~/components/icons/Reviews.vue";
import IconsSettings from "~/components/icons/Settings.vue";
import IconsGroupLink from "~/components/icons/GroupLink.vue";

const route = useRoute();
const router = useRouter();
const { locale } = useI18n();
const localeLang = computed(() => locale.value);
const { apiBaseUrl } = useUrls();
const { isSmaller } = useBreakpoints();
const localePath = useLocalePath();
const { logout, tokenCookie } = useAuth();

const isMobile = computed(() => isSmaller(768));
const {
  isShowCourseVideo,
  showNotification,
  allNotifications,
  allUnreadNotificationsNumber,
  nextCursor,
  isLoading,
} = storeToRefs(useDashboardStore());

const {
  setSearchText,
  setShowNotification,
  nextPage,
  readAllNotifications,
  readSingleNotification,
  unreadSingleNotification,
  deleteAllNotifications,
  deleteSingleNotification,
} = useDashboardStore();
const showDashboard = ref(false);

const showSidebar = ref(false);
const showSearch = ref(true);
const currentIconCom = shallowRef(IconsDashboard);
const searchText = ref("");

const activeRouteName = computed(() => {
  const routeValues: any = {
    dashboard: {
      icon: IconsDashboard,
      search: false,
      name: "Dashboard",
      nameBn: "ড্যাশবোর্ড",
    },
    "dashboard-profile": {
      icon: IconsProfile,
      search: false,
      name: "My Profile",
      nameBn: "আমার প্রোফাইল",
    },
    "dashboard-edit-profile": {
      icon: IconsProfile,
      search: false,
      name: "Edit Profile",
      nameBn: "প্রোফাইল সম্পাদনা করুন",
    },
    "dashboard-enrolled-courses": {
      icon: IconsEnrolledCourses,
      search: true,
      name: "Enrolled Courses",
      nameBn: "ইনরোলড কোর্স",
    },
    "dashboard-order-history": {
      icon: IconsOrderHistory,
      search: false,
      name: "Order History",
      nameBn: "অর্ডার হিস্ট্রি",
    },
    "dashboard-my-quiz-attempts": {
      icon: IconsQuestionAnswer,
      search: true,
      name: "My Quiz Attempts",
      nameBn: "আমার কুইজ আটেম্পট",
    },
    "dashboard-reviews": {
      icon: IconsReviews,
      search: true,
      name: "Reviews",
      nameBn: "রিভিউস",
    },
    "dashboard-question-and-answer": {
      icon: IconsQuestionAnswer,
      search: true,
      name: "Question & Answer",
      nameBn: "প্রশ্ন ও উত্তর",
    },
    "dashboard-settings": {
      icon: IconsSettings,
      search: false,
      name: "Settings",
      nameBn: "সেটিংস",
    },
    "dashboard-group-link": {
      icon: IconsGroupLink,
      search: false,
      name: "Group Link",
      nameBn: "গ্রুপ লিংক",
    },
  };

  const routeName = (route.name as string).slice(0, -5);

  if (routeName in routeValues) {
    const { icon, search, name, nameBn } = routeValues[routeName];
    currentIconCom.value = icon;
    showSearch.value = search;
    return { name, nameBn };
  }
});

useSeoMeta({
  title: () => activeRouteName.value?.name || "Dashboard",
});

const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value;
};

const { data: profileRes, refresh } = await useFetch<any>(
  `${apiBaseUrl}/auth/users`,
  {
    headers: {
      Authorization: `Bearer ${tokenCookie.value}`,
    },
  }
);
const profile = computed(() => profileRes.value);

const handleLogOut = async () => {
  await logout(tokenCookie.value);
};

// For Notifications
const toggleNotification = () => {
  if (showNotification.value) {
    setShowNotification(false);
  } else {
    setShowNotification(true);
  }
};

// For Dropdown
const selectedTooltip = ref<any>({});

const toggleTooltip = (item: any) => {
  if (selectedTooltip.value?.id === item.id) {
    selectedTooltip.value = {};
  } else {
    selectedTooltip.value = item;
  }
};
const isExpanded = (id: any) => {
  return selectedTooltip.value.id === id;
};

const isMouseOver = ref(false);
const mouseOver = () => {
  isMouseOver.value = true;
};
const mouseLeave = () => {
  isMouseOver.value = false;
  setTimeout(() => {
    selectedTooltip.value = {};
  }, 200);
};

const scrollTop = ref(-1);
const isScrollOnBottom = ref(false);
const handleScroll = (e: any) => {
  scrollTop.value = e.target.scrollTop;
  if (
    e.target.scrollHeight - e.target.scrollTop - 10 <=
    e.target.clientHeight
  ) {
    nextPage();
    isScrollOnBottom.value = true;
  } else {
    isScrollOnBottom.value = false;
  }
};
watch(
  () => searchText.value,
  () => {
    setSearchText(searchText.value);
  }
);

watch(showNotification, (newValue) => {
  if (newValue) {
    setTimeout(() => {
      const el = document.getElementById("notificationDiv");
      el?.addEventListener("scroll", handleScroll);
      handleScroll({ target: el });
    }, 300);
  } else {
    const el2 = document.getElementById("notificationDiv");
    el2?.removeEventListener("scroll", handleScroll);
    scrollTop.value = -1;
  }
});

onMounted(async () => {
  if (
    !tokenCookie.value ||
    tokenCookie.value === "" ||
    tokenCookie.value === undefined
  ) {
    router.push(localePath("/auth/login"));
  } else {
    showDashboard.value = true;
  }
  window.scrollTo(0, 0);
});
</script>

<template>
  <div class="custom-container">
    <div v-if="showDashboard" class="flex flex-row h-full w-full relative">
      <div
        v-if="showSidebar"
        @click="toggleSidebar"
        class="fixed top-0 right-0 left-0 bottom-0 z-[3]"
      ></div>
      <div
        class="sidebar mobile-sidebar desktop-sidebar"
        :class="showSidebar ? 'left-0' : '-left-[100%]'"
      >
        <NuxtLink
          :to="localePath('/dashboard')"
          class="link"
          :class="
            activeRouteName?.name === 'Dashboard'
              ? 'text-primary-red'
              : 'text-black'
          "
          @click="isMobile ? (showSidebar = false) : ''"
        >
          <IconsDashboard class="min-w-[40px] h-10" />
          <span class="link-text">{{ $t("dashboard") }}</span>
        </NuxtLink>
        <NuxtLink
          :to="localePath('/dashboard/profile')"
          class="link"
          :class="
            activeRouteName?.name === 'My Profile' ||
            activeRouteName?.name === 'Edit Profile'
              ? 'text-primary-red'
              : 'text-black'
          "
          @click="isMobile ? (showSidebar = false) : ''"
        >
          <IconsProfile class="min-w-[40px] h-10" />
          <span class="link-text">{{ $t("my_profile") }}</span>
        </NuxtLink>
        <NuxtLink
          :to="localePath('/dashboard/enrolled-courses')"
          class="link"
          :class="
            activeRouteName?.name === 'Enrolled Courses'
              ? 'text-primary-red'
              : 'text-black'
          "
          @click="isMobile ? (showSidebar = false) : ''"
        >
          <IconsEnrolledCourses class="min-w-[40px] h-10" />
          <span class="link-text">{{ $t("enrolled_courses") }}</span>
        </NuxtLink>
        <NuxtLink
          :to="localePath('/dashboard/order-history')"
          class="link"
          :class="
            activeRouteName?.name === 'Order History'
              ? 'text-primary-red'
              : 'text-black'
          "
          @click="isMobile ? (showSidebar = false) : ''"
        >
          <IconsOrderHistory class="min-w-[40px] h-10" />
          <span class="link-text">{{ $t("order_history") }}</span>
        </NuxtLink>
        <NuxtLink
          :to="localePath('/dashboard/group-link')"
          class="link"
          :class="
            activeRouteName?.name === 'Group Link'
              ? 'text-primary-red'
              : 'text-black'
          "
          @click="isMobile ? (showSidebar = false) : ''"
        >
          <IconsGroupLink class="min-w-[40px] h-10" />
          <span class="link-text">{{ $t("group_link") }}</span>
        </NuxtLink>
        <div class="hidden">
          <NuxtLink
            :to="localePath('/dashboard/my-quiz-attempts')"
            class="link"
            :class="
              activeRouteName?.name === 'My Quiz Attempts'
                ? 'text-primary-red'
                : 'text-black'
            "
          >
            <IconsQuestionAnswer class="min-w-[40px] h-10" />
            <span class="link-text">{{ $t("my_quiz_attempts") }}</span>
          </NuxtLink>
        </div>
        <div class="hidden">
          <NuxtLink
            :to="localePath('/dashboard/reviews')"
            class="link hidden"
            :class="
              activeRouteName?.name === 'Reviews'
                ? 'text-primary-red'
                : 'text-black'
            "
          >
            <IconsReviews class="min-w-[40px] h-10" />
            <span class="link-text">{{ $t("reviews") }}</span>
          </NuxtLink>
        </div>
        <div class="hidden">
          <NuxtLink
            :to="localePath('/dashboard/question-and-answer')"
            class="link hidden"
            :class="
              activeRouteName?.name === 'Question & Answer'
                ? 'text-primary-red'
                : 'text-black'
            "
          >
            <IconsQuestionAnswer class="min-w-[40px] h-10" />
            <span class="link-text">{{ $t("question_answer") }}</span>
          </NuxtLink>
        </div>
        <div class="h-0.5 bg-black w-full !my-5 hidden"></div>
        <NuxtLink
          :to="localePath('/dashboard/settings')"
          class="!mt-0 hidden"
          :class="
            activeRouteName?.name === 'Settings'
              ? 'text-primary-red'
              : 'text-black'
          "
        >
          <div class="link">
            <IconsSettings class="min-w-[40px] h-10" />
            <span class="link-text">{{ $t("settings") }}</span>
          </div>
        </NuxtLink>
        <button class="link outline-none" @click="handleLogOut">
          <IconsLogOut class="min-w-[40px] h-10" />
          <span class="link-text">{{ $t("logout") }}</span>
        </button>
      </div>

      <div
        class="flex flex-col w-full min-h-[650px] max-h-full py-5 md:pl-10 overflow-x-hidden"
      >
        <div
          v-if="isShowCourseVideo"
          class="flex flex-row items-center justify-between space-x-2 lg:!overflow-x-visible"
        >
          <div class="flex items-center">
            <span
              class="md:!hidden w-10 h-10 bg-[#EDEDED] rounded-[10px] flex items-center justify-center mr-2.5 hover:border border-black"
              @click="toggleSidebar"
            >
              <component :is="currentIconCom" class="w-6"></component>
            </span>
            <p
              class="text-lg md:!text-2xl xl:!text-[28px] 2xl:!text-3xl font-bold"
            >
              {{
                localeLang === "en"
                  ? activeRouteName?.name
                  : activeRouteName?.nameBn
              }}
            </p>
          </div>
          <div
            v-if="showSearch"
            class="hidden w-[50%] h-[60px] pl-3 md:flex flex-row items-center border border-primary-red rounded-[20px]"
          >
            <IconsSearch />
            <input
              v-model="searchText"
              type="text"
              placeholder="Search here..."
              class="pl-3 pr-2 outline-none h-[54px] text-lg w-full rounded-r-[20px]"
            />
          </div>
          <div
            class="flex items-center justify-between space-x-5 md:!space-x-2 lg:!space-x-3 xl:!space-x-4"
          >
            <div class="flex justify-center items-center relative">
              <IconsBell
                @click.stop="toggleNotification"
                class="h-8 w-8 cursor-pointer"
              />
              <span
                v-if="allUnreadNotificationsNumber > 0"
                class="w-2 h-2 bg-primary-red rounded-full absolute top-0 right-0"
              ></span>
              <div
                v-if="showNotification"
                @click.stop=""
                class="absolute top-12 -right-14 lg:-right-32 w-[330px] sm:w-[480px] bg-white rounded-md n-shadow text-[#333333] z-[1] pb-4"
              >
                <div
                  class="flex items-center justify-between px-5 pt-3.5 pb-2.5 border-b border-[#E0E0DD]"
                >
                  <h1 class="text-base font-bold">{{ $t("notification") }}</h1>
                  <div class="flex gap-4">
                    <p class="flex gap-2 items-center text-xs">
                      <button
                        @click="readAllNotifications"
                        :disabled="allUnreadNotificationsNumber === 0"
                        class="text-[#3E87BF]"
                      >
                        {{ $t("mark_all_as_read") }}
                      </button>
                      <span class="text-[#3E87BF]">|</span>
                      <button
                        @click="deleteAllNotifications"
                        :disabled="allNotifications?.length === 0"
                        class="text-[#3E87BF]"
                      >
                        {{ $t("delete_all") }}
                      </button>
                    </p>
                    <IconsCross
                      @click="setShowNotification(false)"
                      class="w-4 cursor-pointer"
                    />
                  </div>
                </div>
                <div
                  id="notificationDiv"
                  class="px-5 max-h-[464px] overflow-y-auto"
                >
                  <template v-if="allNotifications">
                    <ul v-if="allNotifications?.length > 0">
                      <li
                        v-for="(item, index) in allNotifications"
                        :key="item.id"
                      >
                        <div
                          class="flex justify-between items-start gap-2 mt-2.5 py-2.5 px-4 rounded-sm border border-[#EDEDED]"
                          :class="!item.read_at ? 'bg-[#EDEDED]' : ''"
                        >
                          <div
                            @click.stop="
                              item.read_at
                                ? setShowNotification(false)
                                : readSingleNotification(item.id)
                            "
                          >
                            <template
                              v-if="
                                item.type === 'App\\Notifications\\NotifyLogin'
                              "
                            >
                              <div>
                                <p class="line-clamp-2 text-xs font-medium">
                                  You are logged in a new device.
                                </p>
                                <p
                                  class="line-clamp-2 text-[10px] opacity-60 pt-1 break-all"
                                >
                                  Platform: {{ item?.data?.platform }}.
                                </p>
                                <p class="text-[10px] pt-2">
                                  {{ $dateFormat(item?.created_at) }}
                                </p>
                              </div>
                            </template>

                            <template
                              v-if="
                                item.type ===
                                'App\\Notifications\\UploadNewVideo'
                              "
                            >
                              <NuxtLink :to="localePath(item?.data?.link)">
                                <div>
                                  <p class="line-clamp-5 text-xs font-medium">
                                    Updated the course
                                    <span class="font-semibold"
                                      >{{ item?.data.item.title }}
                                    </span>
                                    under
                                    <span class="font-semibold"
                                      >{{ item?.data.unit.title }}
                                    </span>
                                    Titled
                                    <span class="font-semibold"
                                      >{{ item?.data.video.title }}
                                    </span>
                                  </p>
                                  <p
                                    class="line-clamp-2 text-[10px] opacity-60 pt-1 break-all"
                                  >
                                    Go to your Enrolled Courses for details
                                  </p>
                                  <p class="text-[10px] pt-2">
                                    {{ $dateFormat(item?.created_at) }}
                                  </p>
                                </div>
                              </NuxtLink>
                            </template>
                            <template
                              v-if="
                                item.type === 'App\\Notifications\\MessageSend'
                              "
                            >
                              <div>
                                <p class="line-clamp-2 text-xs font-medium">
                                  You got a new message.
                                </p>

                                <p
                                  class="line-clamp-3 text-[10px] opacity-60 pt-1 break-all"
                                >
                                  Message: {{ item?.data?.message }}.
                                </p>
                                <p class="text-[10px] pt-2">
                                  {{ $dateFormat(item?.created_at) }}
                                </p>
                              </div>
                            </template>
                            <template
                              v-else-if="
                                item.type ===
                                'App\\Notifications\\OrderComplete'
                              "
                            >
                              <PaymentNotification :payment="item" />
                            </template>
                            <template
                              v-else-if="
                                item.type ===
                                'App\\Notifications\\PaymentComplete'
                              "
                            >
                              <PaymentNotification :payment="item" />
                            </template>
                            <template
                              v-else-if="
                                item.type ===
                                'App\\Notifications\\PaymentFailed'
                              "
                            >
                              <PaymentNotification :payment="item" />
                            </template>
                          </div>
                          <div
                            @mouseover.stop="mouseOver"
                            @mouseleave.stop="mouseLeave"
                            class="relative"
                          >
                            <div
                              @click.stop="toggleTooltip(item)"
                              class="p-[3px] cursor-pointer text-gray-800 rounded-full hover:bg-slate-200 hover:text-white"
                            >
                              <IconsThreeDots
                                class="min-w-[14px] min-h-[14px] w-1.5 rotate-90"
                              />
                            </div>
                            <div
                              v-if="isExpanded(item.id)"
                              class="text-xs absolute top-4 -right-4 w-max flex flex-col z-10"
                            >
                              <div class="top-0 left-0 w-full h-2"></div>
                              <p
                                class="shadow-xl tooltip-shdow flex flex-col bg-white"
                              >
                                <span
                                  @click.stop="
                                    unreadSingleNotification(item.id)
                                  "
                                  class="py-1.5 px-4 hover:text-white hover:bg-[#3E87BF] cursor-pointer"
                                  >{{ $t("mark_as_unread") }}</span
                                >
                                <span
                                  @click.stop="
                                    deleteSingleNotification(item.id)
                                  "
                                  class="py-1.5 px-4 hover:text-white hover:bg-[#3E87BF] cursor-pointer"
                                  >{{ $t("delete") }}</span
                                >
                              </p>
                            </div>
                          </div>
                        </div>
                        <div
                          v-if="index !== allNotifications.length - 1"
                          class="w-[calc(100%-32px)] h-0.5 mt-1 mx-auto bg-[#E0E0DD]"
                        ></div>
                      </li>
                      <li v-if="!nextCursor" class="pt-2 text-center text-xs">
                        {{ $t("no_more_notification") }}
                      </li>
                      <li
                        v-else-if="isLoading"
                        class="pt-2 text-center text-xs"
                      >
                        {{ $t("loading") }}
                      </li>
                    </ul>
                    <p v-else class="py-4">{{ $t("no_notification") }}</p>
                  </template>
                </div>
              </div>
            </div>
            <div class="flex flex-row items-center md:!space-x-2 lg:!space-x-4">
              <p
                class="md:!flex flex-col text-sm text-right hidden max-w-[300px]"
              >
                <span class="font-semibold">{{ profile?.name }}</span>
                <span class="line-clamp-1">{{ profile?.occupations }}</span>
              </p>
              <NuxtLink :to="localePath('/dashboard/profile')">
                <img
                  v-if="profile?.image"
                  class="w-10 h-10 md:w-[60px] md:h-[60px] object-cover object-top rounded-2xl cursor-pointer"
                  :src="profile?.image"
                  :alt="profile?.name"
                />

                <div v-else>
                  <ClientOnly>
                    <fa
                      class="w-10 h-10 md:w-[60px] md:h-[60px] object-cover object-top rounded-2xl cursor-pointer"
                      :icon="['fa-solid', 'user']"
                  /></ClientOnly>
                </div>
              </NuxtLink>
            </div>
          </div>
        </div>
        <div
          v-if="showSearch"
          class="md:!hidden w-full h-[50px] md:h-60 mt-5 pl-4 flex flex-row items-center border border-primary-red rounded-[20px]"
        >
          <IconsSearch />
          <input
            v-model="searchText"
            type="text"
            placeholder="Search here..."
            class="pl-3 pr-2 outline-none h-[44px] md:h-54 text-lg w-full rounded-r-[20px]"
          />
        </div>
        <NuxtPage :profile="profile" @updateProfile="refresh" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.link {
  @apply flex items-center space-x-4 lg:space-x-6 hover:text-primary-red;
}
.link-text {
  @apply text-lg font-bold;
}
.sidebar {
  @apply h-auto min-h-[100vh] pt-8 pb-[136px] flex flex-col space-y-10 shadow-[4px_0px_4px_0px_rgba(0,0,0,0.25)] transition-all duration-500 ease-in-out;
}
@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar:hover {
    @apply min-w-[230px] xl:!min-w-[270px];
  }
}
@media (max-width: 767px) {
  .mobile-sidebar {
    @apply bg-[#FCFCFC] z-10 w-[270px] absolute top-0 min-h-screen;
  }
}
@media (min-width: 768px) {
  .desktop-sidebar {
    @apply w-[52px] min-w-[52px] lg:!min-w-[240px] xl:!min-w-[280px] whitespace-nowrap overflow-hidden;
  }
}
.n-shadow {
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
}
.has-tooltip {
  @apply relative;
}
.tooltip {
  @apply absolute bg-primary-red text-white z-[100] left-0 top-0 text-left invisible rounded-sm shadow-lg;
}

.has-tooltip:hover .tooltip {
  @apply visible;
  transition: all 0.3s linear;
}

.tooltip-shdow {
  filter: drop-shadow(0px 0px 4px rgba(0, 0, 0, 0.25));
}
</style>
