<script setup lang="ts">
import BlogByCategory from "~/components/blog/BlogByCategory.vue";
import BlogCategory from "~/components/blog/Category.vue";
import type { BlogCategoryResponse, BlogResponse } from "~/types/blog";

useSeoMeta({
  title: "Blogs",
});

const { BLOGS, BLOG_CATEGORIES } = useUrls();
const config = useRuntimeConfig();
const localePath = useLocalePath();

const { data: categoriesRes } = await useFetch<BlogCategoryResponse>(
  `${BLOG_CATEGORIES}?per_page=-1`
);
const categories = computed(() => categoriesRes.value?.data || []);

const { data: featuredRes } = await useFetch<BlogResponse>(
  `${BLOGS}?per_page=1`
);
const featured = computed(() => featuredRes.value?.data?.[0] || null);

const siteUrl = computed(() => {
  return `${config.public.siteUrl}`;
});

useSchemaOrg([
  defineItemList({
    "@context": "https://schema.org",
    "@type": "ItemList",
    name: "Blogs Category",
    itemListElement: categories.value?.map((category, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@type": "Thing",
        name: category.title,
        description: "Blog Category - " + (index + 1),
        url: siteUrl.value + localePath("/blog/category/") + category.slug,
        subjectOf: {
          "@type": "Blog",
          name: "Blogs by Category",
        },
      },
    })),
  }),
  {
    "@context": "https://schema.org/",
    "@type": "BlogPosting",
    "@id": siteUrl.value + localePath("/blog") + "#FeaturedBlogPosting",
    name: featured.value?.title || "Featured Blog",
    description: featured.value?.short_description,
    datePublished: featured.value?.created_at,
    author: {
      "@type": "Person",
      name: featured.value?.createdByUserName || "Sk Mobile School Author",
    },
    publisher: {
      "@type": "Organization",
      "@id": siteUrl.value + "#Organization",
      name: "Sk Mobile School",
      logo: {
        "@type": "ImageObject",
        "@id": siteUrl.value + "/images/logo.webp" + "#OrganizationLogo",
        url: siteUrl.value + "/images/logo.webp",
      },
    },
    image: {
      "@type": "ImageObject",
      "@id": featured.value?.image + "#BlogPostingImage",
      url: featured.value?.image,
    },
    url: `${siteUrl}${localePath(`/blog/${featured.value?.slug}`)}`,
    isPartOf: {
      "@type": "Blog",
      "@id": siteUrl.value + localePath("/blog") + "#Blog",
      name: "Sk Mobile School Blog",
      publisher: {
        "@type": "Organization",
        "@id": siteUrl.value + "#Organization",
        name: "Sk Mobile School",
      },
    },
    keywords: [
      "Sk Mobile School Blog",
      "Mobile Servicing Blog",
      "Mobile Repair Blog",
      "Blog",
    ],
  },
]);

onMounted(async () => {
  await nextTick();
  window.scrollTo(0, 0);
});
</script>

<template>
  <div class="custom-container py-16">
    <div class="space-y-3">
      <p class="text-[30px] leading-10 font-bold text-primary-red text-center">
        {{ $t("blog") }}
      </p>
      <h1 class="text-[40px] leading-[53px] font-bold text-black text-center">
        {{ $t("blog_title") }}
      </h1>
    </div>
    <BlogCategory :categories="categories" class="pt-8" />
    <BlogFeatured :featured="featured" />
    <div class="w-full pt-16 space-y-10">
      <BlogByCategory
        v-for="category in categories"
        :key="category.id"
        :category="category"
      />
    </div>
  </div>
</template>

<style scoped></style>
