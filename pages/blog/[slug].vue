<script setup lang="ts">
import PopularCourseCard from "~/components/blog/PopularCourseCard.vue";
import type { BlogDetailsResponse, BlogPost, BlogResponse } from "~/types/blog";

interface TableOfContentsItem {
  id: string;
  text: string;
  level: number;
}

const config = useRuntimeConfig();
const { BLOGS, apiBaseUrl } = useUrls();
const route = useRoute();
const localePath = useLocalePath();
const { t } = useI18n();

const { data: singleBlogRes, pending: singleBlogPending } =
  await useFetch<BlogDetailsResponse>(`${BLOGS}/${route.params.slug}`);

const singleBlog = computed(() => singleBlogRes.value?.data || null);

// Table of contents functionality
const tableOfContents = ref<TableOfContentsItem[]>([]);
const blogContentRef = ref<HTMLElement>();
const activeHeadingId = ref<string>("");

// Generate table of contents from blog content (SSR compatible)
const generateTableOfContents = () => {
  if (!singleBlog.value?.content) return [];

  // Use regex to extract headings for SSR compatibility
  const content = singleBlog.value.content;
  const toc: TableOfContentsItem[] = [];
  let index = 0;

  // Use replace to iterate through matches to ensure consistent indexing
  content.replace(
    /<(h[1-6])([^>]*)>(.*?)<\/h[1-6]>/gi,
    (match, tag, _attrs, text) => {
      const level = parseInt(tag.charAt(1));
      const cleanText = text.replace(/<[^>]*>/g, "").trim(); // Remove any HTML tags from text
      const id = `heading-${index}`;

      toc.push({
        id,
        text: cleanText,
        level,
      });
      index++;

      return match; // Return original match (we're not actually replacing)
    }
  );

  return toc;
};

// Process blog content to add IDs to headings
const processedBlogContent = computed(() => {
  if (!singleBlog.value?.content) return "";

  let content = singleBlog.value.content;
  let index = 0;

  // Add IDs to headings for anchor links
  content = content.replace(
    /<(h[1-6])([^>]*)>(.*?)<\/h[1-6]>/gi,
    (match, tag, attrs, text) => {
      const id = `heading-${index}`;
      index++;

      // Check if ID already exists in attributes
      if (attrs.includes("id=")) {
        return match;
      }

      return `<${tag}${attrs} id="${id}">${text}</${tag}>`;
    }
  );

  return content;
});

// Smooth scroll to heading
const scrollToHeading = (headingId: string) => {
  if (import.meta.client) {
    const element = document.getElementById(headingId);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }
};

// Generate TOC when blog content is available (works on both server and client)
watch(
  singleBlog,
  (newBlog) => {
    if (newBlog?.content) {
      tableOfContents.value = generateTableOfContents();
    }
  },
  { immediate: true }
);

// Intersection Observer for active heading tracking
const setupIntersectionObserver = () => {
  if (!import.meta.client) return;

  const headings = document.querySelectorAll(
    "h1[id], h2[id], h3[id], h4[id], h5[id], h6[id]"
  );

  if (headings.length === 0) return;

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          activeHeadingId.value = entry.target.id;
        }
      });
    },
    {
      rootMargin: "-20% 0% -35% 0%",
      threshold: 0,
    }
  );

  headings.forEach((heading) => observer.observe(heading));

  // Cleanup function
  return () => {
    headings.forEach((heading) => observer.unobserve(heading));
  };
};

// Also generate on mount for client-side navigation
onMounted(() => {
  if (singleBlog.value?.content) {
    tableOfContents.value = generateTableOfContents();

    // Setup intersection observer after content is rendered
    nextTick(() => {
      setupIntersectionObserver();
    });
  }
});

const { data: relatedPostsRes, pending: relatedPostsPending } =
  await useFetch<BlogResponse>(`${BLOGS}/${route.params.slug}/related-blogs`);
const relatedPosts = computed(() => relatedPostsRes.value?.data || []);

const { data: popularCourseRes } = await useFetch<any>(
  `${apiBaseUrl}/most-popular-courses`
);
const popularCourses = computed(() => popularCourseRes.value?.data);

const postUrl = computed(() => {
  return `${config.public.siteUrl}${localePath(`/blog/${route.params.slug}`)}`;
});

useSeoMeta({
  title: () => singleBlog.value?.title || t("page_not_found"),
  ogTitle: () => singleBlog.value?.title || t("page_not_found"),
  description: () =>
    singleBlog.value ? singleBlog.value?.short_description?.slice(0, 100) : "",
  ogDescription: () =>
    singleBlog.value ? singleBlog.value?.short_description?.slice(0, 100) : "",
  ogImage: () => singleBlog.value?.image || "",
  ogImageAlt: () => singleBlog.value?.title || "",
  twitterCard: "summary_large_image",
});

useSchemaOrg([
  defineArticle({
    headline: singleBlog.value?.title,
    image: singleBlog.value?.image,
    datePublished: singleBlog.value?.created_at,
    dateModified: singleBlog.value?.created_at,
    author: {
      "@type": "Person",
      name: singleBlog.value?.created_by?.name,
    },
    publisher: {
      "@type": "Organization",
      name: "Sk Mobile School",
      logo: {
        "@type": "ImageObject",
        url: "https://skmobileschool.com/images/logo.webp",
      },
    },
  }),
  defineBreadcrumb({
    itemListElement: [
      { name: "Home", item: "/" },
      { name: "Blog", item: "/blog" },
      {
        name: singleBlog.value?.title,
        item: localePath(`/blog/${route.params.slug}`),
      },
    ],
  }),
  defineItemList({
    "@context": "https://schema.org",
    "@type": "ItemList",
    name: "Related Blogs",
    itemListElement: relatedPosts.value?.map((post, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@type": "BlogPosting",
        name: post.title,
        description: post.short_description,
        url: `${config.public.siteUrl}${localePath(`/blog/${post.slug}`)}`,
        image: post.image,
        datePublished: post.created_at,
        author: {
          "@type": "Person",
          name: post.createdByUserName,
        },
      },
    })),
  }),
]);
</script>

<template>
  <div class="custom-container pb-9 min-h-screen">
    <ul
      class="py-16 flex items-start gap-2 text-sm font-medium leading-4 text-[#656565]"
    >
      <li class="flex items-center gap-2">
        <NuxtLink :to="localePath('/')" class="hover:text-black">
          <span>{{ $t("home") }}</span>
        </NuxtLink>
        <IconsChevronDown class="w-2.5 h-2.5 -rotate-90" />
      </li>
      <li class="flex items-center gap-2">
        <NuxtLink :to="localePath('/blog')" class="hover:text-black">
          <span>{{ $t("blogs") }}</span>
        </NuxtLink>
        <IconsChevronDown class="w-2.5 h-2.5 -rotate-90" />
      </li>
      <li>{{ $t("article") }}</li>
    </ul>
    <template v-if="singleBlog && !singleBlogPending">
      <BlogDetailsBlogHero :post="singleBlog" />

      <div
        class="w-full mt-16 flex flex-row gap-10 xl:gap-[128px] font-noto-sans-bengali"
      >
        <div class="max-w-[305px] w-full h-full hidden md:block">
          <div class="flex flex-col pb-8">
            <p class="text-2xl font-bold text-black leading-8">
              {{ $t("table_of_contents") }}
            </p>
            <div class="py-8 transition-all duration-300">
              <nav v-if="tableOfContents.length > 0">
                <ul class="space-y-2">
                  <li
                    v-for="(item, index) in tableOfContents"
                    :key="item.id"
                    :class="[
                      'text-base font-medium leading-[21px] transition-all duration-200',
                      item.level === 1 && index !== 0 ? 'ml-0 pt-4' : '',
                      item.level === 2 && index !== 0 ? 'ml-0 pt-4' : '',
                      item.level === 3 && index !== 0 ? 'ml-4' : '',
                      item.level === 4 && index !== 0 ? 'ml-4' : '',
                      item.level === 5 && index !== 0 ? 'ml-4' : '',
                      item.level === 6 && index !== 0 ? 'ml-4' : '',
                    ]"
                  >
                    <button
                      @click="scrollToHeading(item.id)"
                      :class="[
                        'text-left transition-colors duration-200 cursor-pointer block w-full pr-2',
                        activeHeadingId === item.id
                          ? 'text-primary-red'
                          : 'text-black hover:text-primary-red',
                      ]"
                      :title="item.text"
                    >
                      {{ item.text }}
                    </button>
                  </li>
                </ul>
              </nav>
              <div v-else class="text-sm text-gray-500 italic">
                {{ $t("No_headings_found_in_this_article") }}
              </div>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <NuxtLink
              :to="`https://www.facebook.com/share.php?u=${postUrl}`"
              target="_blank"
            >
              <SvgFacebook class="w-6 h-6" />
            </NuxtLink>
            <NuxtLink
              :to="`http://twitter.com/share?&url=${postUrl}&text=Sk Mobile School blog about - ${singleBlog?.title}.`"
              target="_blank"
            >
              <SvgTwitter class="w-6 h-6" />
            </NuxtLink>
            <NuxtLink
              :to="`https://www.linkedin.com/sharing/share-offsite/?url=${postUrl}`"
              target="_blank"
            >
              <SvgLinkedin class="w-6 h-6" />
            </NuxtLink>
            <NuxtLink
              :to="`mailto:?subject=${singleBlog?.title}&body=${postUrl}`"
              target="_blank"
            >
              <SvgGmail class="w-6 h-6" />
            </NuxtLink>
            <IconsShare class="w-5 h-auto" />
          </div>
          <div class="pt-16 font-roboto">
            <h6 class="text-2xl font-bold text-black leading-8 mb-6">
              {{ $t("popular_courses") }}
            </h6>
            <ClientOnly>
              <BaseSlider
                :sliders="popularCourses"
                :showNavigation="false"
                :wrapAround="true"
                :isSingleSlider="true"
                carouselItemClass="p-1"
                paginationClass="!mt-4"
              >
                <template #slider="{ data }: { data: any }">
                  <PopularCourseCard :card="data" class="h-full text-start" />
                </template>
              </BaseSlider>
              <template #fallback>
                <PopularCourseCard :card="popularCourses?.[0]" />
              </template>
            </ClientOnly>
          </div>
        </div>
        <div
          ref="blogContentRef"
          class="w-full h-full flex-grow blog-details font-noto-sans-bengali"
          v-html="processedBlogContent"
        ></div>
      </div>

      <div
        v-if="relatedPosts.length > 0 && !relatedPostsPending"
        class="w-full mt-16"
      >
        <h6
          class="text-[32px] text-black leading-[38px] pb-3 border-b-2 border-[#78787826] font-noto-sans-bengali"
        >
          {{ $t("related_posts") }}
        </h6>
        <div class="pt-8">
          <ClientOnly>
            <BaseSlider
              :sliders="relatedPosts"
              :showNavigation="false"
              :wrapAround="true"
              carouselItemClass="p-2.5"
              paginationClass="!mt-1.5"
            >
              <template #slider="{ data }: { data: BlogPost }">
                <BlogCard :post="data" class="h-full text-start" />
              </template>
            </BaseSlider>
            <template #fallback>
              <div
                class="grid grid-cols-1 sm:grid-cols-2 min-[1200px]:grid-cols-3 gap-5"
              >
                <BlogCard
                  v-for="blog in relatedPosts.slice(0, 3)"
                  :key="blog.id"
                  :post="blog"
                  class="h-full w-full blog-card"
                />
              </div>
            </template>
          </ClientOnly>
        </div>
      </div>
    </template>

    <div v-else class="flex justify-center items-center pt-20">
      <span class="text-xl md:text-3xl xl:text-5xl">{{
        singleBlogPending ? $t("loading") : $t("page_not_found")
      }}</span>
    </div>
  </div>
</template>

<style scoped>
/* Base container */
:deep(.blog-details) {
  @apply text-base leading-relaxed text-gray-800;
}

/* Headings */
:deep(.blog-details h1) {
  @apply text-3xl md:text-4xl font-semibold mt-8 mb-4 leading-tight text-gray-900;
}

:deep(.blog-details h2) {
  @apply text-2xl md:text-3xl font-semibold mt-6 mb-3 text-gray-900;
}

:deep(.blog-details h3) {
  @apply text-xl md:text-2xl font-semibold mt-5 mb-2 text-gray-900;
}

:deep(.blog-details h4) {
  @apply text-lg md:text-xl font-semibold mt-4 mb-2 text-gray-800;
}

:deep(.blog-details h5) {
  @apply text-base md:text-lg font-semibold mt-3 mb-2 text-gray-800;
}

:deep(.blog-details h6) {
  @apply text-sm md:text-base font-semibold mt-2 mb-1 text-gray-700 uppercase tracking-wide;
}

/* Paragraphs */
:deep(.blog-details p) {
  @apply text-base md:text-lg leading-relaxed mb-2.5 text-gray-700;
}

/* Links */
:deep(.blog-details a) {
  @apply text-blue-600 hover:underline hover:text-blue-800 transition-colors;
}

/* Lists */
:deep(.blog-details ul) {
  @apply list-disc list-inside mb-4 pl-4;
}

:deep(.blog-details ol) {
  @apply list-decimal list-inside mb-4 pl-4;
}

:deep(.blog-details li) {
  @apply mb-1;
}

/* Images */
:deep(.blog-details img) {
  @apply rounded-md w-full my-6 h-auto;
  max-width: max-content;
}

/* Blockquotes */
:deep(.blog-details blockquote) {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-6;
}

/* Code & Pre */
:deep(.blog-details pre),
:deep(.blog-details code) {
  @apply bg-gray-100 text-sm font-mono rounded px-2 py-1 text-gray-800;
}

:deep(.blog-details pre) {
  @apply p-4 overflow-x-auto my-4;
}

/* Horizontal Rule */
:deep(.blog-details hr) {
  @apply my-6 border-gray-300;
}

/* Tables */
:deep(.blog-details table) {
  @apply w-full border-collapse my-6 text-left text-sm md:text-base;
}

:deep(.blog-details th),
:deep(.blog-details td) {
  @apply border border-gray-300 px-3 py-2;
}

:deep(.blog-details th) {
  @apply bg-gray-100 font-semibold;
}

.blog-card:nth-child(2) {
  display: none;
}

@media (min-width: 640px) {
  .blog-card:nth-child(2) {
    display: block;
  }
}

.blog-card:last-child {
  display: none;
}

@media (min-width: 1200px) {
  .blog-card:last-child {
    display: block;
  }
}

/* Heading scroll target offset */
:deep(.blog-details h1),
:deep(.blog-details h2),
:deep(.blog-details h3),
:deep(.blog-details h4),
:deep(.blog-details h5),
:deep(.blog-details h6) {
  scroll-margin-top: 5rem;
}
</style>
