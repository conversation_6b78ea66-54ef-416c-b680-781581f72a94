<script setup lang="ts">
import BlogCategory from "~/components/blog/Category.vue";
import type {
  BlogCategoryResponse,
  BlogPost,
  BlogResponse,
} from "~/types/blog";

useSeoMeta({
  title: "Blogs by Category",
});

// Composables
const { BLOGS, BLOG_CATEGORIES } = useUrls();
const route = useRoute();
const config = useRuntimeConfig();
const localePath = useLocalePath();
// States
const blogs = ref<BlogPost[]>([]);
const currentPage = ref(1);
const totalPages = ref(1);
const totalBlogs = ref(1);
const perPageBlogs = ref(9);
const isLoading = ref(false);
const blogsRef = ref<HTMLDivElement | null>(null);

const category = computed(() => route.params?.category);
const query = computed(() => route.query);
const siteUrl = computed(() => {
  return `${config.public.siteUrl}`;
});

const fetchBlogs = async () => {
  try {
    isLoading.value = true;
    let url = `${BLOGS}?slug=${category.value}&page=${
      query.value?.page || 1
    }&per_page=${perPageBlogs.value}`;
    if (query.value?.search) {
      url += `&query=${query.value?.search}`;
    }
    const res = await $fetch<BlogResponse>(url);
    if (res) {
      blogs.value = res.data;
      currentPage.value = res?.meta?.current_page || 1;
      totalPages.value = res?.meta?.last_page || 1;
      perPageBlogs.value = res?.meta?.per_page || 9;
      totalBlogs.value = res?.meta?.total || 0;
    }
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};
const setPageRoute = (page: number) => {
  let basePath = localePath(`/blog/category/${category.value}?page=${page}`);
  if (query.value?.search) {
    navigateTo(`${basePath}&search=${query.value.search}`);
  } else {
    navigateTo(basePath);
  }
  window.scrollTo(0, 0);
};
const goToPrevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    setPageRoute(currentPage.value);
  }
};
const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    setPageRoute(currentPage.value);
  }
};
const gotToPage = (page: number) => {
  if (currentPage.value !== page) {
    currentPage.value = page;
    setPageRoute(page);
  }
};
await fetchBlogs();

const { data: categoriesRes } = await useFetch<BlogCategoryResponse>(
  `${BLOG_CATEGORIES}?per_page=-1`
);
const categories = computed(() => categoriesRes.value?.data || []);

// Watchers
watch(
  () => route.query,
  async () => {
    await fetchBlogs();
  },
  { deep: true }
);

onMounted(async () => {
  await nextTick();
  window.scrollTo(0, 0);
});

watchEffect(() => {
  useSchemaOrg([
    categories.value?.length &&
      defineItemList({
        "@context": "https://schema.org",
        "@type": "ItemList",
        name: "Blogs Category",
        itemListElement: categories.value?.map((category, index) => ({
          "@type": "ListItem",
          position: index + 1,
          item: {
            "@type": "Thing",
            name: category.title,
            description: "Blog Category - " + (index + 1),
            url: siteUrl.value + localePath("/blog/category/") + category.slug,
            subjectOf: {
              "@type": "Blog",
              name: "Blogs by Category",
            },
          },
        })),
      }),
    blogs.value?.length &&
      defineItemList({
        "@context": "https://schema.org",
        "@type": "ItemList",
        name: "Blogs by Category - " + category.value,
        itemListElement: blogs.value?.map((post, index) => ({
          "@type": "ListItem",
          position: index + 1,
          item: {
            "@context": "https://schema.org",
            "@id": `${siteUrl.value}${localePath(`/blog/${post.slug}`)}`,
            "@type": "BlogPosting",
            name: post.title,
            description: post.short_description,
            url: `${siteUrl.value}${localePath(`/blog/${post.slug}`)}`,
            image: {
              "@type": "ImageObject",
              "@id": post.image + "#BlogPostingImage",
              url: post.image,
            },
            datePublished: post.created_at,
            author: {
              "@type": "Person",
              name: post.createdByUserName || "Sk Mobile School Author",
            },
            publisher: {
              "@type": "Organization",
              "@id": siteUrl.value + "#Organization",
              name: "Sk Mobile School",
              logo: {
                "@type": "ImageObject",
                "@id":
                  siteUrl.value + "/images/logo.webp" + "#OrganizationLogo",
                url: siteUrl.value + "/images/logo.webp",
              },
            },
            isPartOf: {
              "@type": "Blog",
              "@id": siteUrl.value + localePath("/blog") + "#Blog",
              name: "Sk Mobile School Blog",
              publisher: {
                "@type": "Organization",
                "@id": siteUrl.value + "#Organization",
                name: "Sk Mobile School",
              },
            },
          },
        })),
      }),
  ]);
});
</script>

<template>
  <div class="custom-container py-16">
    <BlogCategory :categories="categories" />

    <div ref="blogsRef" class="py-8 h-full min-h-[50vh] flex flex-col">
      <Transition name="fade" mode="out-in">
        <div
          v-if="isLoading"
          class="w-full h-full flex justify-center items-center min-h-[50vh]"
        >
          <span class="text-xl md:text-3xl">Loading...</span>
        </div>
        <div
          v-else-if="!isLoading && blogs?.length"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 w-full h-full"
        >
          <BlogCard v-for="blog in blogs" :key="blog.id" :post="blog" />
        </div>
        <div
          v-else
          class="w-full h-full min-h-[50vh] flex justify-center items-center"
        >
          <span class="text-xl md:text-3xl">{{ $t("no_data_found") }}</span>
        </div>
      </Transition>
    </div>

    <BasePagination
      v-if="totalBlogs > perPageBlogs && blogs?.length > 0"
      :current-page="currentPage"
      :total-pages="totalPages"
      @go-to-page="gotToPage"
      @go-to-prev-page="goToPrevPage"
      @go-to-next-page="goToNextPage"
    />
  </div>
</template>

<style scoped></style>
