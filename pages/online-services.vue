<script setup lang="ts">
import defaultThumb from "~/assets/img/default/video-thumbnail.webp";

useSeoMeta({
  title: "Online Services",
});

const { ONLINE_PROVIDERS, apiBaseUrl, PAGE_OVERVIEW_SECTION } = useUrls();
const nuxtApp = useNuxtApp();
const localePath = useLocalePath();
const { isSmaller } = useBreakpoints();

// API call
const { data: pageOverViewRes } = await useFetch<any>(
  `${PAGE_OVERVIEW_SECTION}/online-services`
);
const pageOverView = computed(() => pageOverViewRes.value?.data);
useSeoMeta({
  ogImage: () => pageOverView?.value?.cover_image || "images/logo.webp",
});
const perPageCourses = ref(9);

const { data } = await useFetch<any>(
  `${apiBaseUrl}/courses?per_page=${perPageCourses.value}`
);
const courseSlider = computed(() => data.value?.data);
const totalPages = computed(() => data.value?.meta.last_page);

const selectedInstructor = ref<any>({});

const setSelectedInstructor = (instructor: any) => {
  selectedInstructor.value = instructor;
};

const onlineProviders = ref(<any>[]);
const isLoading = ref(false);
const getAllOnlineProviders = async () => {
  isLoading.value = true;

  try {
    const response = await $fetch<any>(ONLINE_PROVIDERS);

    if (response?.data) {
      onlineProviders.value = response.data;

      setSelectedInstructor(
        onlineProviders.value && onlineProviders.value.length > 0
          ? onlineProviders.value[0]
          : {}
      );
    }
  } catch (error: any) {
    nuxtApp.$toast("clear");
    nuxtApp.$toast("error", {
      message: error?.data?.message?.code || "Failed to load online providers",
      className: "toasted-bg-success",
    });
  } finally {
    isLoading.value = false;
  }
};

await getAllOnlineProviders();

const activeInstructor = (instructor: any) => {
  return selectedInstructor?.value.id === instructor.id;
};

const isMobile = computed(() => isSmaller(768));
const isMenuOpen = ref(true);
const toggleDropdown = () => {
  if (isMobile.value) {
    isMenuOpen.value = !isMenuOpen.value;
  }
  return;
};
const closeMenu = () => {
  if (isMobile.value) {
    isMenuOpen.value = false;
  } else {
    isMenuOpen.value = true;
  }
  return;
};
watch(isMobile, (val) => {
  if (val) {
    isMenuOpen.value = false;
  } else {
    isMenuOpen.value = true;
  }
});

closeMenu();

const pageOverViewMedia = ref(false);

useSchemaOrg([
  defineItemList({
    "@context": "https://schema.org/",
    "@type": "Service",
    name: pageOverView.value?.title,
    description: pageOverView.value?.content,
  }),
]);

onMounted(() => {
  window.scrollTo(0, 0);
});
</script>

<template>
  <main class="custom-container">
    <section class="pt-10 pb-20">
      <div
        v-if="pageOverView"
        class="flex flex-col-reverse md:flex-row items-center gap-10 xl:gap-20"
      >
        <div class="w-full md:w-1/2 h-full">
          <p class="text-sm md:text-base lg:text-lg">
            {{ pageOverView.sub_title }}
          </p>
          <h1
            class="text-primary-red font-bold text-[32px] md:text-[40px] xl:text-[60px] py-4 mt-0 break-words"
          >
            {{ pageOverView.title }}
          </h1>
          <div
            class="text-base md:text-2xl text-justify"
            v-html="pageOverView.content"
          ></div>
        </div>

        <div v-if="pageOverView.media_link" class="w-full md:w-1/2 h-full">
          <div v-if="pageOverView.type === 'video'" class="bg-[#15151F]">
            <Transition name="page" mode="out-in">
              <div v-if="!pageOverViewMedia" class="relative">
                <img
                  class="w-full aspect-video z-[4]"
                  :src="
                    pageOverView?.cover_image
                      ? pageOverView?.cover_image
                      : defaultThumb
                  "
                  alt=""
                />
                <div
                  class="absolute top-0 inset-0 m-auto z-[5] flex justify-center items-center"
                >
                  <div
                    @click="pageOverViewMedia = true"
                    class="cursor-pointer flex justify-center items-center animate-button w-14 aspect-square rounded-full pl-1 text-white"
                  >
                    <IconsPlay class="w-[18px] aspect-[3/4]" />
                  </div>
                </div>
              </div>
              <iframe
                v-else
                class="w-full aspect-video"
                :src="`${pageOverView.media_link}?rel=0&autoplay=1`"
                frameborder="0"
                allowfullscreen
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              ></iframe>
            </Transition>
          </div>
          <img
            v-else
            :src="pageOverView.media_link"
            class="object-cover w-full aspect-video"
            width="100%"
            :alt="pageOverView.title"
          />
        </div>
      </div>
      <NoPageFound v-else minHeight="420" />
    </section>

    <section class="pt-5 pb-10 lg:pb-20">
      <h2 class="text-3xl font-bold text-center text-[#EC1F27]">
        {{ $t("instructor_title") }}
      </h2>
      <div
        class="flex flex-col md:flex-row gap-2.5 md:gap-10 xl:gap-20 pt-4 md:pt-10 h-auto min-h-[600px]"
      >
        <div
          class="relative flex flex-col w-[200px] min-w-[200px] md:min-w-[240px] lg:min-w-[360px] xl:min-w-[420px] text-base md:text-lg lg:text-[30px] rounded-[10px] md:overflow-hidden provider-card-top-shadow"
        >
          <div
            v-if="isMenuOpen"
            @click="toggleDropdown"
            class="fixed top-0 right-0 left-0 bottom-0 z-[1] md:hidden"
          ></div>
          <h3
            @click="toggleDropdown"
            class="justify-between md:justify-center min-h-[40px] lg:min-h-[70px] px-2.5 md:px-4 flex items-center w-full bg-[#EC1F27] text-white font-semibold"
            :class="isMenuOpen ? 'rounded-t-[10px]' : ' rounded-[10px]'"
          >
            <span>{{ $t("instructor_list") }}</span>
            <IconsChevronDown
              class="transition-all duration-500 ease-in-out w-5 text-white md:hidden"
              :class="isMenuOpen ? 'rotate-180' : 'rotate-0 '"
            />
          </h3>
          <ul
            v-if="isMenuOpen && Object.keys(onlineProviders).length > 0"
            class="mobile-menu provider-list flex flex-col overflow-auto min-h-full max-h-[calc(100vh-240px)]"
          >
            <li
              v-for="(item, index) in onlineProviders"
              :key="item.id"
              @click="setSelectedInstructor(item)"
              :class="activeInstructor(item) ? 'bg-[#D9D9D9]' : ''"
            >
              <span class="line-clamp-1">{{ item.name }}</span>
            </li>
          </ul>
          <NoPageFound
            v-else-if="isMenuOpen && Object.keys(onlineProviders).length === 0"
            minHeight="420"
          />
        </div>
        <div
          class="provider-card-top-shadow relative flex-grow rounded-[10px] p-10 pr-4 xl:pr-6 xl:p-20 flex"
        >
          <div
            v-if="isLoading"
            class="isLoadingIcon flex flex-col w-full justify-center items-center"
          >
            <IconsIsLoading />
            <p class="pt-28 text-xl font-bold">{{ $t("please_wait") }}</p>
          </div>
          <div
            class="justify-start items-start"
            v-else-if="selectedInstructor?.description"
            v-html="selectedInstructor?.description"
          ></div>
          <div
            v-else
            class="w-full h-full flex flex-col justify-center items-center"
          >
            <IconsNoData class="" />
            <p class="text-lg pt-5">{{ $t("no_data_text") }}</p>
          </div>
          <div
            v-if="selectedInstructor?.social_link"
            class="w-full flex items-center justify-end space-x-4 px-4 sm:px-10 absolute bottom-6 right-0"
          >
            <NuxtLink
              v-for="(media, index) in selectedInstructor?.social_link"
              :key="index"
              :to="media.link"
              target="_blank"
            >
              <SvgFacebook
                v-if="media.name?.toLowerCase() === 'facebook'"
                class="w-10 h-10"
              />
              <SvgWhatsapp
                v-else-if="media.name?.toLowerCase() === 'whatsapp'"
                class="w-10 h-10"
              />
              <SvgTwitter
                v-else-if="media.name?.toLowerCase() === 'twitter'"
                class="w-10 h-10"
              />
              <SvgLinkedin
                v-else-if="media.name?.toLowerCase() === 'linkedin'"
                class="w-10 h-10"
              />
              <SvgGmail
                v-else-if="media.name?.toLowerCase() === 'gmail'"
                class="w-10 h-10"
              />
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <section v-if="courseSlider" class="pt-5 pb-20 space-y-20">
      <div
        class="flex flex-col items-center space-y-3 w-full md:w-[700px] mx-auto"
      >
        <h2 class="text-3xl font-bold text-center text-[#EC1F27]">
          {{ $t("admission_going_title") }}
        </h2>
        <h3 class="text-2xl font-normal text-center">
          {{ $t("admission_going_subtitle") }}
        </h3>
        <p class="text-[18px] font-normal text-center">
          {{ $t("admission_going_text_2") }}
        </p>
      </div>
      <CourseDetails :courses="courseSlider" />
      <div v-if="totalPages > 1" class="flex items-center justify-center !my-5">
        <NuxtLink
          :to="localePath(`/browse-course`)"
          class="inline-block rounded-full hover:text-white hover:border-primary-red hover:bg-primary-red text-primary-red opacity-100 px-5 py-2 text-base font-bold leading-normal transition-all duration-150 ease-in-out border border-black"
        >
          {{ $t("see_more") }}
        </NuxtLink>
      </div>
    </section>
  </main>
</template>

<style scoped>
.provider-card-top-shadow {
  box-shadow: 0px -4px 4px 0px #00000040, 0px 4px 4px 0px #00000040;
}
ul.provider-list li {
  @apply min-h-[32px] md:min-h-[40px] lg:min-h-[70px] flex justify-center items-center w-full hover:bg-[#D9D9D9] text-black cursor-pointer px-2.5 md:px-4;
}
@media (max-width: 767px) {
  .mobile-menu {
    @apply absolute top-[40px] right-0 z-50 bg-white w-full shadow-xl rounded-b-[10px] overflow-y-auto;
  }
}
</style>
