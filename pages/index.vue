<script setup>
import { Viewer } from "@photo-sphere-viewer/core";
import "@photo-sphere-viewer/core/index.css";

useSeoMeta({
  title: "Home",
});

const nuxtApp = useNuxtApp();
const localePath = useLocalePath();
const { t } = useI18n();
const config = useRuntimeConfig();

const {
  HOME_HERO,
  SUCCESS_RATES,
  SLIDER,
  SUCCESS_STUDENT_VIDEOS,
  SEE_INSTITUTES,
  apiBaseUrl,
} = useUrls();

// API call
const { data: pageOverViewRes } = await useFetch(HOME_HERO);
const pageOverView = computed(() => pageOverViewRes.value?.data);
useSeoMeta({
  ogImage: () => pageOverView?.value?.cover_image || "images/logo.webp",
});
const perPageCourses = ref(6);

const { data } = await useFetch(
  `${apiBaseUrl}/courses?per_page=${perPageCourses.value}`
);
const courseSlider = computed(() => data.value?.data);
const totalPages = computed(() => data.value?.meta.last_page);

const { data: popularCourseSliderRes } = await useFetch(
  `${apiBaseUrl}/most-popular-courses`
);
const popularCourseSlider = computed(() => popularCourseSliderRes.value?.data);

const { data: successRatesRes } = await useFetch(SUCCESS_RATES);
const successRates = computed(() => successRatesRes.value?.data);
const { data: courseDetailsSliderRes } = await useFetch(
  `${SLIDER}/course-details`
);
const courseDetailsSlider = computed(() => courseDetailsSliderRes.value?.data);
const { data: consultationSliderRes } = await useFetch(
  `${SLIDER}/consultation`
);
const consultationSlider = computed(() => consultationSliderRes.value?.data);

const { data: practicalSliderRes } = await useFetch(`${SLIDER}/practical`);
const practicalSlider = computed(() => practicalSliderRes.value?.data);

const { data: techniciansSliderRes } = await useFetch(`${SLIDER}/technicians`);
const techniciansSlider = computed(() => techniciansSliderRes.value?.data);

const { data: successReportsSliderRes } = await useFetch(
  `${SLIDER}/success-reports`
);
const successReportsSlider = computed(
  () => successReportsSliderRes.value?.data
);

const { data: successStudentVideosRes } = await useFetch(
  SUCCESS_STUDENT_VIDEOS
);
const successStudentVideos = computed(
  () => successStudentVideosRes.value?.data
);

const consultation = {
  section: "consultation",
  title: t("consultation_slider_title"),
  description: t("consultation_slider_text"),
  bgColor: "bg-[#FCEEEE]",
};
const practical = {
  section: "practical",
  title: t("practical_slider_title"),
  description: t("practical_slider_text"),
  bgColor: "bg-[#F6F6FF]",
};
const technicians = {
  section: "technicians",
  title: t("technicians_slider_title"),
  description: t("technicians_slider_text"),
  bgColor: "bg-[#FFF9F4]",
};

useSchemaOrg([
  defineItemList({
    "@type": "ItemList",
    name: $t("our_courses_title"),
    itemListElement: courseSlider.value?.map((course, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@context": "https://schema.org/",
        "@type": "Course",
        name: course.title,
        description: course.subtitle,
        url: `${config.public.siteUrl}${localePath(
          `/precheckout/${course.slug}`
        )}`,
        provider: {
          "@type": "Organization",
          name: "Sk Mobile School",
          sameAs: config.public.siteUrl,
          logo: {
            "@type": "ImageObject",
            url: "https://skmobileschool.com/images/logo.webp",
          },
        },
      },
    })),
  }),

  defineItemList({
    "@type": "ItemList",
    name: "Our Course Details",
    itemListElement: courseDetailsSlider.value?.map((details, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@context": "https://schema.org/",
        "@type": "ImageObject",
        name: details.title,
        description: details.sub_title,
        image: details.media_link,
      },
    })),
  }),

  defineItemList({
    "@type": "ItemList",
    name: $t("success_story_title"),
    itemListElement: successStudentVideos.value?.map((story, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@context": "https://schema.org",
        "@type": "VideoObject",
        name: story.title,
        thumbnailUrl: [story.cover_image],
      },
    })),
  }),

  defineItemList({
    "@type": "ItemList",
    name: $t("special_service_title"),
    itemListElement: successRates.value?.map((rate, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@context": "https://schema.org/",
        "@type": "Service",
        name: rate.title,
        description: rate.content,
      },
    })),
  }),

  defineItemList({
    "@type": "ItemList",
    name: consultation.title,
    itemListElement: consultationSlider.value?.map((con, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@context": "https://schema.org/",
        "@type": "Service",
        image: con.type === "image" ? con.media_link : con.cover_image,
      },
    })),
  }),

  defineItemList({
    "@type": "ItemList",
    name: practical.title,
    itemListElement: practicalSlider.value?.map((pr, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@context": "https://schema.org/",
        "@type": "Service",
        image: pr.type === "image" ? pr.media_link : pr.cover_image,
      },
    })),
  }),

  defineItemList({
    "@type": "ItemList",
    name: technicians.title,
    itemListElement: techniciansSlider.value?.map((te, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@context": "https://schema.org/",
        "@type": "Service",
        image: te.type === "image" ? te.media_link : te.cover_image,
      },
    })),
  }),

  defineItemList({
    "@type": "ItemList",
    name: "Our Success Reports",
    itemListElement: successReportsSlider.value?.map((report, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@context": "https://schema.org/",
        "@type": "Service",
        name: report.title,
        description: report.sub_title,
        image: report.type === "image" ? report.media_link : report.cover_image,
      },
    })),
  }),
]);

const seeInstitutes = ref("");
const getInstitute = async () => {
  const { data, pending, error } = await useFetch(SEE_INSTITUTES);

  const setData = () => {
    if (!pending.value) {
      if (data.value) {
        seeInstitutes.value = data.value.data;
        setTimeout(() => {
          if (seeInstitutes.value.is_360_degree_content === 1) {
            if (process.client) {
              const viewer = new Viewer({
                container: document.getElementById("viewer"),
                panorama: seeInstitutes.value.media_url,
              });
            }
          }
        }, 500);
      } else if (error.value) {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: error.value.data.message,
          className: "toasted-bg-success",
        });
      }
    } else {
      setTimeout(() => {
        setData();
      }, 300);
    }
  };
  setData();
};
getInstitute();
</script>

<template>
  <main class="bg-total">
    <section class="custom-container pt-20 pb-[192px]">
      <BasePageOverview
        v-if="pageOverView"
        :pageOverView="pageOverView"
        :groupButton="true"
      />
      <NoPageFound v-else minHeight="420" />
    </section>

    <section v-if="courseSlider" class="custom-container max-w-[1238px] pb-32">
      <div class="flex flex-col items-center text-center px-2 md:px-16">
        <h2 class="heading-primary">{{ $t("our_courses_title") }}</h2>
        <p class="text-primary pt-2.5 pb-[34px]">
          {{ $t("our_courses_text") }}
        </p>
      </div>
      <div class="min-h-[500px]">
        <ClientOnly>
          <BaseSlider
            :sliders="courseSlider"
            :showNavigation="false"
            :wrapAround="true"
          >
            <template #slider="{ data }">
              <CardCourseSlider :card="data" />
            </template>
          </BaseSlider>
        </ClientOnly>
      </div>
    </section>

    <section v-if="courseDetailsSlider">
      <div class="w-full aspect-video md:aspect-[16/7]">
        <ClientOnly>
          <BaseSlider
            :sliders="courseDetailsSlider"
            :isSingleSlider="true"
            :showPagination="false"
            :wrapAround="true"
            carouselItemClass=""
            navigationClass="course-details"
          >
            <template #slider="{ data }">
              <CardImageVideo
                :card="data"
                :showDescription="false"
                mediaClass="w-full aspect-video md:aspect-[16/7]"
              />
            </template> </BaseSlider
        ></ClientOnly>
      </div>
    </section>

    <section
      v-if="successStudentVideos"
      class="custom-container max-w-[1234px] py-32"
    >
      <div class="flex flex-col items-center text-center px-2">
        <h2 class="heading-primary text-[#A2D55A]">
          {{ $t("success_story_title") }}
        </h2>
        <p class="text-primary pt-2.5 pb-3.5">
          {{ $t("success_story_text") }}
        </p>
      </div>
      <div class="min-h-[498px]">
        <ClientOnly>
          <BaseSlider
            :sliders="successStudentVideos"
            :showNavigation="false"
            :wrapAround="true"
            paginationClass="success-student-videos"
          >
            <template #slider="{ data }">
              <CardSuccessStory
                :card="data"
                mediaClass="w-full aspect-[378/213] rounded-t-2xl"
              />
            </template>
          </BaseSlider>
        </ClientOnly>
      </div>
    </section>

    <section class="custom-container pb-32 px-10">
      <div class="flex flex-col items-center text-center px-2">
        <h2 class="heading-primary">{{ $t("special_service_title") }}</h2>
        <p class="text-primary pt-2.5 pb-[30px] w-full max-w-[850px]">
          {{ $t("special_service_text") }}
        </p>
      </div>
      <div class="min-h-[520px]">
        <ClientOnly>
          <BaseSlider
            :sliders="successRates"
            :showPagination="false"
            :activeItemFocused="true"
            navigationClass="special-service"
            carouselItemClass="px-4 py-10"
            :wrapAround="true"
          >
            <template #slider="{ data, index }">
              <CardSpecialService :card="data" :cardIndex="index" />
            </template> </BaseSlider
        ></ClientOnly>
      </div>
    </section>

    <section v-if="popularCourseSlider" class="custom-container pb-20 hidden">
      <div class="flex flex-col items-center text-center">
        <h2 class="sub-heading mt-0">{{ $t("popular_courses_title") }}</h2>
        <p class="text-small w-full md:w-1/2 pt-4 pb-10">
          {{ $t("popular_courses_text") }}
        </p>
      </div>
      <CourseDetails :courses="popularCourseSlider.slice(0, perPageCourses)" />
    </section>

    <section v-if="courseSlider" class="custom-container pb-28 max-w-[1200px]">
      <div class="flex flex-col items-center text-center px-2">
        <h2 class="heading-primary">{{ $t("admission_going_title") }}</h2>
        <p class="text-primary pt-2.5 pb-12 w-full md:w-1/2">
          {{ $t("admission_going_text") }}
        </p>
      </div>

      <CoursesAll :courses="courseSlider" />

      <div v-if="totalPages > 1" class="flex items-center justify-center pt-10">
        <NuxtLink
          :to="localePath(`/browse-course`)"
          class="red-button bg-[#FEE] text-black text-sm md:text-base border-2 border-primary-red/50 w-44"
        >
          {{ $t("see_more") }}
        </NuxtLink>
      </div>
    </section>

    <section class="custom-container space-y-24 pb-32">
      <CardTraning
        v-if="consultationSlider"
        :slider="consultationSlider"
        :section="consultation"
      />
      <CardTraning
        v-if="practicalSlider"
        :slider="practicalSlider"
        :section="practical"
        cardClass="flex-col lg:flex-row-reverse"
        bgDotsClass="right-0 top-0 lg:top-auto lg:bottom-0 "
      />
      <CardTraning
        v-if="techniciansSlider"
        :slider="techniciansSlider"
        :section="technicians"
      />
    </section>

    <section
      class="custom-container space-y-10 text-center lg:text-left hidden"
    >
      <div>
        <h2 class="sub-heading pb-10">
          {{ consultation.title }}
        </h2>
        <div
          class="flex flex-col-reverse lg:flex-row gap-10 xl:gap-16 2dx:gap-24 items-center md:px-10 lg:px-0"
        >
          <BaseImageVideoCarousel
            class="w-full md:max-w-[630px] lg:max-w-full lg:w-1/2"
            :slider="consultationSlider ? consultationSlider : []"
            :show-navigation="true"
          />
          <p class="w-full lg:w-1/2 text-sm md:text-base lg:text-2xl">
            {{ consultation.description }}
          </p>
        </div>
      </div>

      <div>
        <div
          class="flex flex-col lg:flex-row gap-10 xl:gap-16 2dx:gap-24 items-center"
        >
          <div class="w-full lg:w-1/2 space-y-4 md:px-10 lg:px-0">
            <h2 class="sub-heading">{{ practical.title }}</h2>

            <p class="text-sm md:text-base lg:text-2xl">
              {{ practical.description }}
            </p>
          </div>
          <BaseImageVideoCarousel
            class="w-full md:max-w-[630px] lg:max-w-full lg:w-1/2"
            :slider="practicalSlider ? practicalSlider : []"
            :show-navigation="true"
          />
        </div>
      </div>

      <div>
        <div
          class="flex flex-col-reverse lg:flex-row gap-10 xl:gap-16 2dx:gap-24 items-center"
        >
          <BaseImageVideoCarousel
            class="w-full md:max-w-[630px] lg:max-w-full lg:w-1/2"
            :slider="techniciansSlider ? techniciansSlider : []"
            :show-navigation="true"
          />
          <div class="w-full lg:w-1/2 space-y-4 md:px-10 lg:px-0">
            <h2 class="sub-heading">
              {{ technicians.title }}
            </h2>
            <p class="text-sm md:text-base lg:text-2xl">
              {{ technicians.description }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <section
      v-if="seeInstitutes && seeInstitutes?.media_url !== null"
      class="custom-container pb-32"
    >
      <div class="flex flex-col items-center text-center px-2">
        <h2 class="heading-primary">{{ $t("see_institute_title") }}</h2>
        <p class="text-primary pt-2.5 pb-12 w-full md:w-3/4">
          {{ $t("see_institute_text") }}
        </p>
      </div>
      <div class="px-4 sm:px-8 md:px-12">
        <div
          v-if="seeInstitutes.is_360_degree_content === 1"
          id="viewer"
          class="w-full max-h-[80vh] aspect-video rounded-[20px] overflow-hidden"
        ></div>
        <img
          v-else-if="seeInstitutes.media_type === 'image'"
          class="object-fill object-top w-full aspect-video rounded-[20px]"
          :src="seeInstitutes.media_url"
          alt="Sk Mobile School Institute Image"
        />
        <BaseEmbadedVideoPlayer
          v-else
          playerId="institureVideoPlayer"
          videoTitle="Sk Mobile School Institute Video"
          :videoUrl="seeInstitutes?.media_url"
          :coverImage="seeInstitutes?.cover_image"
          playBtnClass="w-14 xl:w-16"
        />
      </div>
    </section>

    <section class="custom-container space-y-24 pb-32">
      <CardSuccessReport
        v-if="successReportsSlider"
        :card="successReportsSlider"
        cardClass="flex-col lg:flex-row-reverse"
        bgDotsClass="right-0 top-0 lg:top-auto lg:bottom-0 "
      />
    </section>
  </main>
</template>
<style scoped>
.bg-total {
  @apply bg-contain 2xl:bg-[length:100%_100%] bg-no-repeat bg-[url("/assets/img/default/all.webp")] min-h-[7212px];
  background-position: top center;
}
</style>
