<script setup>
definePageMeta({
  middleware: (to, from) => {
    // This will be executed on route navigation
  }
});

useSeoMeta({
  title: "Payment Success",
});
import { storeToRefs } from "pinia";
import { useAuthStore } from "~/stores/auth";
import { useCartDataStore } from "~/stores/cartData";
import { useDashboardStore } from "~/stores/dashboard";
import { useSubscriptionPackageStore } from "~/stores/subscriptionPackage";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const {
  apiBaseUrl,
  USER_SUBSCRIPTIONS,
  DOWNLOAD_FILES,
  FIRMWARE_FILES,
  DELETE_DEVICE_TOKEN,
} = useUrls();
const localePath = useLocalePath();
const { t } = useI18n();
const tokenCookie = useCookie("token");
const redirectUrl = useCookie("redirectUrl");
const showSuccessBody = ref(false);
const isProcessing = ref(false);
const showAppModal = ref(false);
const { searchText } = storeToRefs(useDashboardStore());
const { setIsShowCourseVideo } = useDashboardStore();
const { user } = storeToRefs(useAuthStore());
const allVideoCourses = ref([]);
const { setRedirectUrl } = useAuth();
const { isActiveSubscription } = storeToRefs(useSubscriptionPackageStore());
const { setSubscriptionDataForApi } = useCartDataStore();
const deviceIdCookie = useCookie("deviceId");

const order = ref({});

const isLoading = ref(true);
const fileNeedsToBuy = ref(false);
const forceToLogin = ref(false);
const goToBilling = ref(false);

const fileDetail = ref({});

const getFileDetails = () => {
  const { data, pending, error } = useFetch(
    `${FIRMWARE_FILES}/${order.value?.details[0]?.item?.slug}/details`,
    {
      headers: {
        Authorization: `Bearer ${tokenCookie.value ? tokenCookie.value : ""}`,
      },
    }
  );
  const setData = () => {
    if (!pending.value) {
      if (data.value) {
        fileDetail.value = data.value;
      } else if (error.value) {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: error.value?.data.message,
          className: "toasted-bg-success",
        });
        isProcessing.value = false;
      }
    } else {
      setTimeout(() => {
        setData();
      }, 300);
    }
  };
  setData();
};
const deleteDeviceToken = async () => {
  try {
    await $fetch(DELETE_DEVICE_TOKEN, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${tokenCookie.value}`,
      },
      body: {
        device_id: deviceIdCookie.value,
      },
    });
    return true;
  } catch (error) {
    return false;
  }
};
const handleTransaction = async () => {
  console.log("handleTransaction called",route.query.val_id);
  if (showSuccessBody.value) {
    isProcessing.value = true;
    setTimeout(async () => {
      try {
        const { data } = await useFetch(
          `${apiBaseUrl}/payment/sslcommerz/success`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
              "x-device-id": deviceIdCookie.value,
            },
            body: {
              transaction_id: route.query.tran_id,
            },
          }
        );
        if (data.value) {
          order.value = data.value.order;
          deleteDeviceToken();
          setTimeout(() => {
            if (order.value?.order_type === "file") {
              getFileDetails();
            }
          }, 200);
          nuxtApp.$toast("clear");
          nuxtApp.$toast("success", {
            message: data.value?.message,
            className: "toasted-bg-success",
          });
          isProcessing.value = false;
        }
      } catch (error) {
        console.log(error);
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: error?.response?._data?.message,
          className: "toasted-bg-alert",
        });
        isProcessing.value = false;
      }
    }, 500);
  }
};

const checkDownloadCondition = async (
  downloadFile,
  isThisFeatured,
  requestFileSize
) => {
  try {
    isLoading.value = true;
    if (!tokenCookie.value || tokenCookie.value === "") {
      setRedirectUrl(route.fullPath);
      fileNeedsToBuy.value = true;
      forceToLogin.value = true;
      nuxtApp.$toast("clear");
      nuxtApp.$toast("error", {
        message: t("messages.login_first"),
        className: "toasted-bg-alert",
      });
    } else {
      if (isActiveSubscription.value) {
        let response = await $fetch(`${USER_SUBSCRIPTIONS}`, {
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
        });

        if (
          response.conditions.total_band_width >
            response.conditions.used_total_band_width &&
          response.conditions.total_file > response.conditions.used_total_file
        ) {
          if (
            response.conditions.total_band_width >=
            response.conditions.used_total_band_width + requestFileSize
          ) {
            if (
              response.conditions.is_all_file ||
              (response.conditions.is_featured_file && isThisFeatured) ||
              (response.conditions.is_none_featured_file && !isThisFeatured)
            ) {
              let downloadUrl = await $fetch(
                `${DOWNLOAD_FILES}/${downloadFile.id}/download`,
                {
                  headers: {
                    Authorization: `Bearer ${tokenCookie.value}`,
                  },
                  responseType: "blob",
                }
              );
              isLoading.value = false;
              const link = document.createElement("a");
              link.href = URL.createObjectURL(downloadUrl);

              link.download = "download.zip";
              link.click();

              URL.revokeObjectURL(link.href);
              // window.open(downloadUrl.url, "_blank");
            } else {
              isLoading.value = false;
              setRedirectUrl(route.fullPath);
              router.push(localePath("/subscription"));

              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: t("messages.upgrade_subscription"),
                className: "toasted-bg-alert",
              });
            }
          } else {
            isLoading.value = false;
            setRedirectUrl(route.fullPath);
            router.push(localePath("/subscription"));

            nuxtApp.$toast("clear");
            nuxtApp.$toast("error", {
              message: t("messages.exceeded_download_limit"),
              className: "toasted-bg-alert",
            });
          }
        } else {
          isLoading.value = false;
          setRedirectUrl(route.fullPath);
          router.push(localePath("/subscription"));

          nuxtApp.$toast("clear");
          nuxtApp.$toast("error", {
            message: t("messages.upgrade_subscription"),
            className: "toasted-bg-alert",
          });
        }
      } else {
        const { data, pending, error } = useFetch(
          `${DOWNLOAD_FILES}/${downloadFile.id}/download`,
          {
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
            },
          }
        );

        const setData = () => {
          if (!pending.value) {
            if (data.value && data.value?.file_url_type === "link") {
              window.open(data.value.url, "_blank");
            } else if (data.value && !data.value?.file_url_type) {
              const link = document.createElement("a");
              link.href = URL.createObjectURL(data.value);

              link.download = "download.zip";
              link.click();

              URL.revokeObjectURL(link.href);
              setRedirectUrl("/");
            } else if (error.value) {
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: error.value?.data.message,
                className: "toasted-bg-success",
              });

              const makeSubscriptionDataForApi = {
                type: "file",
                downloadFileDetails: downloadFile,
                downloadFileId: [
                  {
                    item_id: downloadFile.id,
                  },
                ],
              };
              setSubscriptionDataForApi(makeSubscriptionDataForApi);

              if (error.value?.statusCode === 403) {
                setTimeout(() => {
                  router.push(
                    localePath(
                      `/download/${order.value?.details[0]?.item?.slug}`
                    )
                  );
                  fileNeedsToBuy.value = true;
                }, 300);
              }
            }
          } else {
            setTimeout(() => {
              setData();
            }, 300);
          }
        };
        setData();
      }
    }
  } catch (error) {
    console.log(error);
    nuxtApp.$toast("clear");
    nuxtApp.$toast("error", {
      message: error?.response?._data?.message,
      className: "toasted-bg-alert",
    });
  }
};

onMounted(async () => {
  // Prevent back navigation - lock history
  const preventBackNavigation = () => {
    history.pushState(null, null, location.href);
  };
  
  // Initial push to history
  history.pushState(null, null, location.href);
  
  // Listen for popstate event (back button)
  window.addEventListener("popstate", preventBackNavigation);
  
  // Additional safeguard - keep pushing state periodically
  const intervalId = setInterval(() => {
    if (history.state === null) {
      history.pushState(null, null, location.href);
    }
  }, 100);
  
  // Store references for cleanup
  if (process.client) {
    window.__preventBack = preventBackNavigation;
    window.__historyInterval = intervalId;
  }

  if (
    !tokenCookie.value ||
    tokenCookie.value === "" ||
    tokenCookie.value === undefined
  ) {
    router.push(localePath("/auth/login"));
  } else {
    showSuccessBody.value = true;
    handleTransaction();
  }
});

onBeforeUnmount(() => {
  // Clean up the event listener and interval
  if (process.client) {
    if (window.__preventBack) {
      window.removeEventListener("popstate", window.__preventBack);
      delete window.__preventBack;
    }
    if (window.__historyInterval) {
      clearInterval(window.__historyInterval);
      delete window.__historyInterval;
    }
  }
});
</script>
<template>
  <div class="min-h-screen custom-container">
    <div
      v-if="isProcessing"
      class="w-full h-full flex flex-col justify-center items-center mt-20"
    >
      <IconsIsLoading />
      <p class="text-lg pt-24">{{ $t("please_wait") }}</p>
    </div>

    <div
      v-else-if="showSuccessBody"
      class="border shadow-xl mt-16 p-6 md:p-12 w-full lg:w-1/2 mx-auto"
    >
      <div
        class="h-full flex flex-col space-y-4 justify-center items-center text-4xl"
      >
        <img class="w-[90px] aspect-square" src="/images/ok.svg" />
        <p class="center">{{ $t("congratulations") }}</p>
        <p
          class="text-center text-base md:text-[20px] pb-[25px] w-full sm:w-[400px]"
        >
          {{ $t("payment_successfully_completed_check_email") }}
        </p>
      </div>
      <div class="border-y border-black">
        <div v-if="order?.order_type === 'video'">
          <p class="text-base md:text-[20px] pt-5 font-semibold">
            {{ $t("start_your_favourite_course") }}
          </p>

          <div class="mt-5 flex justify-between my-5">
            <div class="flex items-center">
              <img
                class="w-[60px] h-[50px]"
                v-if="
                  order.details[0].item.banner_url &&
                  order.details[0].item.banner_url !== 'null'
                "
                :src="order.details[0].item.banner_url"
                alt="banner_url"
              />
              <img
                v-else
                src="/images/course-details/courseIcon.png"
                class="w-[60px] h-[50px] border-b border-[#d1d7dc]"
                alt=""
                srcset=""
              />
              <p class="text-base md:text-[20px] pl-4">
                {{ order.details[0].item.title }}
              </p>
            </div>

            <button
              @click="showAppModal = true"
              class="bg-primary-red p-3 text-white ml-5"
            >
              {{ $t("start_now") }}
            </button>
          </div>
          <OpenAppModal
            v-if="showAppModal"
            :showAppModal="showAppModal"
            @closeAppModal="showAppModal = false"
          />
        </div>
        <div v-if="order?.order_type === 'file'">
          <p class="text-base md:text-[20px] pt-5 font-semibold">
            {{ $t("download_your_fav_file") }}
          </p>

          <div class="mt-5 flex items-center justify-between my-5">
            <div class="flex items-center">
              <img
                src="/icons/device.svg"
                class="w-11 aspect-auto"
                alt="device Sk Info"
                srcset=""
              />
              <p class="text-base md:text-[20px] pl-4 line-clamp-1">
                {{ order.details[0]?.item?.title }}
              </p>
            </div>

            <button
              @click="
                checkDownloadCondition(
                  fileDetail?.data,
                  fileDetail?.data?.id,
                  fileDetail?.data?.is_featured,
                  fileDetail?.data?.file_size
                )
              "
              class="bg-primary-red p-3 text-white ml-5"
            >
              {{ $t("download") }}
            </button>
          </div>
        </div>
      </div>
      <div class="mt-5 flex flex-col gap-5 text-base md:text-xl">
        <div class="flex items-center justify-between">
          <span>{{ $t("order_id") }}</span>
          <span>{{ order.details[0].order_id }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span>{{ $t("total_price") }}</span> <span>{{ order.total }} TK</span>
        </div>
        <div class="flex items-center justify-between">
          <span>{{ $t("account_name") }}</span> <span>{{ user?.name }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span>{{ $t("account_email") }}</span> <span>{{ user?.email }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
