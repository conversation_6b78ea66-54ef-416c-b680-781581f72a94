export interface TableOfContentsItem {
  id: string;
  text: string;
  level: number;
}

export interface ProcessedContentData {
  toc: TableOfContentsItem[];
  processedContent: string;
}

/**
 * Composable for handling table of contents functionality
 * Provides methods to process HTML content and generate TOC data
 */
export const useTableOfContents = () => {
  /**
   * Process HTML content and generate table of contents with IDs
   * @param content - HTML content string
   * @returns Object containing TOC array and processed content with IDs
   */
  const processHeadingsData = (content: string): ProcessedContentData => {
    if (!content) return { toc: [], processedContent: "" };

    const toc: TableOfContentsItem[] = [];
    let index = 0;

    // Process content and generate TOC in a single pass
    const processedContent = content.replace(
      /<(h[1-6])([^>]*)>(.*?)<\/h[1-6]>/gi,
      (match, tag, attrs, text) => {
        const level = parseInt(tag.charAt(1));
        const cleanText = text.replace(/<[^>]*>/g, "").trim();
        const id = `heading-${index}`;

        // Add to table of contents
        toc.push({
          id,
          text: cleanText,
          level,
        });

        // Check if ID already exists in attributes
        if (attrs.includes("id=")) {
          index++;
          return match;
        }

        index++;
        return `<${tag}${attrs} id="${id}">${text}</${tag}>`;
      }
    );

    return { toc, processedContent };
  };

  /**
   * Smooth scroll to a heading element by ID
   * @param headingId - The ID of the heading element to scroll to
   */
  const scrollToHeading = (headingId: string) => {
    if (import.meta.client) {
      const element = document.getElementById(headingId);
      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }
  };

  /**
   * Setup intersection observer for active heading tracking
   * @param activeHeadingId - Ref to store the currently active heading ID
   * @returns Cleanup function to remove the observer
   */
  const setupIntersectionObserver = (activeHeadingId: Ref<string>) => {
    if (!import.meta.client) return;

    const headings = document.querySelectorAll(
      "h1[id], h2[id], h3[id], h4[id], h5[id], h6[id]"
    );

    if (headings.length === 0) return;

    const observer = new IntersectionObserver(
      (entries: IntersectionObserverEntry[]) => {
        // Find the heading that is most visible
        let mostVisibleEntry: IntersectionObserverEntry | null = null;
        let maxIntersectionRatio = 0;

        entries.forEach((entry: IntersectionObserverEntry) => {
          if (
            entry.isIntersecting &&
            entry.intersectionRatio > maxIntersectionRatio
          ) {
            maxIntersectionRatio = entry.intersectionRatio;
            mostVisibleEntry = entry;
          }
        });

        // If we have a most visible entry, set it as active
        if (mostVisibleEntry !== null) {
          const target = (mostVisibleEntry as any).target as HTMLElement;
          if (target && target.id) {
            activeHeadingId.value = target.id;
          }
        } else {
          // If no heading is intersecting, find the closest one above the viewport
          const scrollTop =
            window.pageYOffset || document.documentElement.scrollTop;
          let closestHeading: Element | null = null;
          let closestDistance = Infinity;

          headings.forEach((heading) => {
            const rect = heading.getBoundingClientRect();
            const headingTop = rect.top + scrollTop;

            if (headingTop <= scrollTop + 100) {
              // 100px offset for better UX
              const distance = scrollTop - headingTop;
              if (distance < closestDistance) {
                closestDistance = distance;
                closestHeading = heading;
              }
            }
          });

          if (closestHeading) {
            const target = closestHeading as HTMLElement;
            activeHeadingId.value = target.id;
          }
        }
      },
      {
        rootMargin: "-10% 0% -80% 0%",
        threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
      }
    );

    headings.forEach((heading) => observer.observe(heading));

    // Cleanup function
    return () => {
      headings.forEach((heading) => observer.unobserve(heading));
    };
  };

  return {
    processHeadingsData,
    scrollToHeading,
    setupIntersectionObserver,
  };
};
