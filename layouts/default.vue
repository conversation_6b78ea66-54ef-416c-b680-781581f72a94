<script setup>
import { useAuthStore } from "~/stores/auth";
import { useDashboardStore } from "~/stores/dashboard";
import { useIndexStore } from "~/stores/index";

const route = useRoute();
const { apiBaseUrl, ME } = useUrls();
const { locale, localeProperties } = useI18n();
const switchLocalePath = useSwitchLocalePath();
const localePath = useLocalePath();
const { setShowNotification, setAllNotifications } = useDashboardStore();
const { joinToChannel } = useNotification();
const { setCountryDropdown, setUser } = useAuthStore();
const { fetchCommonSettings } = useIndexStore();
const { logout } = useAuth();
await fetchCommonSettings();
const iosPayment = computed(() => {
  return Boolean(route.query?.iosToken || route.query?.iosPayment);
});

const headerRef = ref(null);
const localeLang = computed(() => locale.value);
const localeEmoji = computed(() => localeProperties.value.emoji);
const { data: imei } = await useFetch(`${apiBaseUrl}/get-imei-checker`);

const closeMenu = () => {
  headerRef.value?.closeMenu();
  closeAllModal();
};
const closeAllModal = () => {
  setShowNotification(false);
  setCountryDropdown(false);
};
// For Imei Checker Dropdown
const isImeiExpand = ref(false);
const toggleImeiExpand = () => {
  closeMenu();
  isImeiExpand.value = !isImeiExpand.value;
};
const isMouseOver = ref(false);
const mouseOver = () => {
  isMouseOver.value = true;
};
const mouseLeave = () => {
  isMouseOver.value = false;
  setTimeout(() => {
    isImeiExpand.value = false;
  }, 200);
};
// For Language Dropdown
const isLanguageExpand = ref(false);
const isMouseOverLanguage = ref(false);
const mouseOnLanguage = () => {
  isMouseOverLanguage.value = true;
};
const mouseLeaveFromLanguage = () => {
  isMouseOverLanguage.value = false;
  setTimeout(() => {
    isLanguageExpand.value = false;
  }, 200);
};
const toggleLanguage = () => {
  closeMenu();
  isLanguageExpand.value = !isLanguageExpand.value;
};
// Security
const disableF12 = () => {
  document.onkeydown = function (event) {
    if (event.key === "F12") {
      event.preventDefault();
    }
  };
};
const disableSelectCopyRightClickAndPrint = () => {
  // Disable text selection
  document.onselectstart = function () {
    return false;
  };

  // Disable right-click
  document.oncontextmenu = function () {
    return false;
  };

  // Prevent printing
  window.onbeforeprint = function () {
    alert("Printing is disabled for this page.");
    return false;
  };
};

watch(
  () => route.fullPath,
  (routerPath) => {
    const greCaptchaBadge = document.getElementsByClassName("grecaptcha-badge");

    const setRecaptchaBadgeStyle = () => {
      if (greCaptchaBadge) {
        if (
          routerPath.includes("/auth/login") ||
          routerPath.includes("/auth/register")
        ) {
          greCaptchaBadge[0].style.opacity = "1";
        } else {
          greCaptchaBadge[0].style.opacity = "0";
        }
      } else {
        setTimeout(() => {
          setRecaptchaBadgeStyle();
        }, 200);
      }
    };
    setRecaptchaBadgeStyle();
  }
);

onMounted(async () => {
  await nextTick();
  disableF12();
  disableSelectCopyRightClickAndPrint();
  // check for token
  const token = useCookie("token");
  if (token.value) {
    try {
      const data = await $fetch(ME, {
        headers: {
          Authorization: `Bearer ${token.value}`,
        },
      });
      if (data && data.id) {
        joinToChannel(data.id);
        setAllNotifications();
        setUser(data);
      }
    } catch (e) {
      await logout(token.value);
    }
  }
});
</script>

<template>
  <div @click="closeMenu" class="no-select">
    <div class="bg-primary-red w-full">
      <div class="custom-container">
        <div class="flex justify-between items-center h-12 md:h-14">
          <div class="h-full flex items-center">
            <div
              v-if="!iosPayment"
              @mouseover.stop="mouseOver"
              @mouseleave.stop="mouseLeave"
              class="dropdown menu-item relative w-auto"
            >
              <p
                @click.stop="toggleImeiExpand"
                class="min-h-[32px] py-1 font-semibold flex items-center justify-between cursor-pointer gap-2 border-2 text-white border-white rounded-full px-2 sm:px-4 text-sm"
              >
                <span>{{ $t("imei_Checker") }}</span>
                <IconsCaretDown
                  class="min-w-[12px] w-3 aspect-square transition-all duration-300 ease-in-out"
                  :class="isImeiExpand ? 'rotate-180' : 'rotate-0'"
                />
              </p>
              <div
                v-if="isImeiExpand"
                class="dropdown-content rounded-[15px] absolute left-0 top-8 text-center w-max pr-5 min-w-[220px] max-w-[calc(100vw-32px)] shadow-2xl bg-white overflow-hidden"
              >
                <div
                  class="overflow-y-auto max-h-[200px] w-[calc(100%+20px)] bg-white"
                >
                  <NuxtLink
                    v-for="(item, i) in imei.data"
                    :to="item.link"
                    target="_blank"
                    :key="i"
                    class="text-black h-10 flex items-center w-full justify-start"
                  >
                    <p class="px-3 py-1 text-start line-clamp-1 break-all">
                      {{ item.title }}
                    </p>
                  </NuxtLink>
                </div>
              </div>
            </div>
          </div>
          <div class="h-full flex items-center">
            <div class="flex items-center gap-1.5 md:gap-4">
              <NuxtLink v-if="!iosPayment" :to="localePath('/cart')">
                <IconsWishlist class="w-6 h-6 text-white" />
              </NuxtLink>
              <div
                @mouseover.stop="mouseOnLanguage"
                @mouseleave.stop="mouseLeaveFromLanguage"
                class="dropdown menu-item relative"
              >
                <p
                  @click.stop="toggleLanguage"
                  class="w-[90px] sm:w-[120px] py-1 font-bold flex items-center justify-between cursor-pointer gap-2 border-2 text-white border-white rounded-full px-2 sm:px-2.5 text-sm"
                >
                  <span class="hidden sm:block">{{ localeEmoji }}</span>
                  <span>{{ localeLang === "bn" ? "বাংলা" : "English" }}</span>
                  <IconsCaretDown
                    class="w-3 aspect-square transition-all duration-300 ease-in-out"
                    :class="isLanguageExpand ? 'rotate-180' : 'rotate-0'"
                  />
                </p>
                <div
                  v-if="isLanguageExpand"
                  class="dropdown-content whitespace-nowrap rounded-[15px] absolute left-0 top-8 text-center w-full shadow-2xl overflow-hidden"
                >
                  <NuxtLink
                    @click="mouseLeaveFromLanguage"
                    :to="switchLocalePath('bn')"
                    class="text-black flex items-center w-full justify-start"
                  >
                    <p class="px-3 sm:px-4 py-2 text-start">🇧🇩 বাংলা</p>
                  </NuxtLink>
                  <NuxtLink
                    @click="mouseLeaveFromLanguage"
                    :to="switchLocalePath('en')"
                    class="text-black flex items-center w-full justify-start"
                  >
                    <p class="px-3 sm:px-4 py-2 text-start">🇺🇸 English</p>
                  </NuxtLink>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sticky top-0 z-[9999]">
      <TheHeader class="bg-white" ref="headerRef" @click.stop="closeAllModal" />
    </div>

    <slot />
    <NuxtLink
      v-if="!route.path.includes('/dashboard')"
      to="https://wa.me/message/3PLDQ33YFCIXP1"
      target="_blank"
      class="fixed bottom-10 right-4 z-50 p-3 rounded-full"
    >
      <img
        class="w-12 lg:w-16 aspect-square object-contain"
        src="/icons/whatsapp.svg"
        alt="Sk Mobile School Whatsapp"
      />
    </NuxtLink>
    <AdBlockDetector />
    <BaseVideoPlayerModal />
    <TheFooter v-if="!$route.path.includes('/dashboard')" />
  </div>
</template>
<style>
@import "~/assets/css/slider.css";

.no-select {
  user-select: none;
  -webkit-user-select: none;
  /* Chrome and Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* IE10+Edge */
}

.menu-item {
  @apply text-white;
}

.dropdown-content {
  @apply text-sm bg-white z-[99999];
}

.dropdown-content a:hover {
  @apply bg-black text-white rounded-[15px];
}

/* ScrollBar style */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-thumb {
  background: #ec1f27;
  border-radius: 10px;
}

::-webkit-scrollbar-track {
  background: #e8e8e8;
  border-radius: 10px;
}

/* For Global */
html {
  scroll-behavior: smooth;
}

.subscription iframe {
  @apply w-full h-full aspect-video;
}

.break-word {
  word-break: break-word;
}

.leading-default {
  line-height: normal;
}

.psv-download-button {
  display: none !important;
}
</style>
