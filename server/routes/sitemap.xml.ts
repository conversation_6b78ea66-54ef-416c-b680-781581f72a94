// server/routes/sitemap.xml.ts
export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig();
  const siteUrl = config.public.siteUrl;
  const lastmod = new Date("2025-10-16").toISOString();

  const sitemaps = [
    { loc: `${siteUrl}/sitemap/en.xml`, lastmod },
    { loc: `${siteUrl}/sitemap/bn.xml`, lastmod },
  ];

  const xml = `<?xml version="1.0" encoding="UTF-8"?>
    <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
      ${sitemaps
        .map(
          (item) => `
        <sitemap>
          <loc>${item.loc}</loc>
          <lastmod>${item.lastmod}</lastmod>
        </sitemap>`
        )
        .join("\n")}
    </sitemapindex>`;

  setResponseHeader(event, "Content-Type", "application/xml");

  return xml;
});
