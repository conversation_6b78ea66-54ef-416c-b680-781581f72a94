// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-09-22",
  app: {
    head: {
      title: "Best Mobile Servicing Training Institute",
      titleTemplate: "%s | SK Mobile School",
      htmlAttrs: {
        lang: "en",
      },
      script: [
        {
          src: `https://www.google.com/recaptcha/enterprise.js?render=${process.env.NUXT_PUBLIC_RECAPTCHA_SITE_KEY}`,
          async: true,
          defer: true,
        },
        {
          src: "https://js.pusher.com/8.0.1/pusher.min.js",
          async: true,
          defer: true,
        },
      ],
      meta: [
        { charset: "utf-8" },
        { name: "viewport", content: "width=device-width, initial-scale=1" },
        {
          name: "keywords",
          content: process.env.NUXT_PUBLIC_META_KEYWORDS || "",
        },
        { property: "og:type", content: "website" },
        {
          name: "description",
          content:
            "SK Mobile School - Professional mobile device repair training institute in Bangladesh. Offering comprehensive courses in smartphone, tablet repair. Learn from industry experts with hands-on training",
        },
        {
          key: "og:image",
          property: "og:image",
          content: `${process.env.NUXT_PUBLIC_SITE_URL}/images/logo.webp`,
        },
        {
          key: "og:image:alt",
          property: "og:image:alt",
          content: "SK Mobile School Logo",
        },
      ],
      link: [
        {
          rel: "stylesheet",
          href: "https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@100..900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap",
        },
        {
          rel: "icon",
          type: "image/x-icon",
          href: `${process.env.NUXT_PUBLIC_SITE_URL}/images/favicon.ico`,
        },
      ],
    },
    pageTransition: { name: "page", mode: "out-in" },
    layoutTransition: { name: "layout", mode: "out-in" },
  },

  runtimeConfig: {
    apiUrl: process.env.NUXT_PUBLIC_API_URL,
    authSecret: "secret",
    public: {
      appName: process.env.NUXT_PUBLIC_APP_NAME,
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL,
      apiUrl: process.env.NUXT_PUBLIC_API_URL,
      googleMapKey: process.env.NUXT_PUBLIC_GOOGLE_MAP_KEY,
      workflow: process.env.NUXT_PUBLIC_WORKFLOW,
      recaptchaKey: process.env.NUXT_PUBLIC_RECAPTCHA_SITE_KEY,
      pusherAppKey: process.env.NUXT_PUBLIC_PUSHER_APP_KEY,
      pusherHost: process.env.NUXT_PUBLIC_PUSHER_HOST,
      pusherPort: process.env.NUXT_PUBLIC_PUSHER_PORT,
      gtm: {
        id: process.env.NUXT_PUBLIC_GTAG_ID || "",
      },
      metapixel: {
        default: { id: process.env.NUXT_PUBLIC_FACEBOOK_PIXEL_ID || "" },
      },
    },
  },

  devtools: { enabled: true },

  css: ["~/assets/css/main.css", "v-calendar/dist/style.css"],

  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },

  modules: [
    "dayjs-nuxt",
    "@nuxtjs/seo",
    "@pinia/nuxt",
    "@nuxtjs/i18n",
    "@vueuse/nuxt",
    "nuxt-meta-pixel",
    "@vee-validate/nuxt",
    "@zadigetvoltaire/nuxt-gtm",
  ],

  // ogImage: {
  //   enabled: false,
  // },

  dayjs: {
    plugins: ["relativeTime", "utc", "timezone"],
    defaultTimezone: "Dhaka",
  },

  veeValidate: {
    autoImports: true,
    componentNames: {
      Form: "VeeForm",
      Field: "VeeField",
      FieldArray: "VeeFieldArray",
      ErrorMessage: "VeeErrorMessage",
    },
  },

  i18n: {
    locales: [
      {
        code: "bn",
        name: "Bengali",
        emoji: "🇧🇩",
        file: "bn-BD.json",
      },
      {
        code: "en",
        name: "English",
        emoji: "🇺🇸",
        file: "en-US.json",
      },
    ],
    langDir: "lang",
    defaultLocale: "bn",
    detectBrowserLanguage: false,
  },

  nitro: {
    routeRules: {
      "/server/**": {
        proxy: `${process.env.NUXT_PUBLIC_API_URL}/**`,
      },
    },
  },
  vue: {
    compilerOptions: {
      isCustomElement: (tag) => ["marquee"].includes(tag),
    },
  },
  site: {
    url: process.env.NUXT_PUBLIC_SITE_URL,
    name: process.env.NUXT_PUBLIC_APP_NAME,
    indexable: process.env.NUXT_PUBLIC_WORKFLOW === "live",
  },
  robots: {
    disallow: [
      "/billing",
      "/browse-hardware",
      "/cart",
      "/dashboard*",
      "/subscription",
      "/download/old-slug",
      "/payment/*",
      "/privacy-policy",
      "/refund-policy",
      "/cancellation-policy",
      "/terms-of-use",
      "/auth/*",
    ],
    sitemap: ["/sitemap.xml"],
  },
  sitemap: {
    enabled: false,
  },
  schemaOrg: {
    identity: {
      "@type": "Organization",
      "@context": "https://schema.org",
      "@id": "https://skmobileschool.com/#organization",
      name: "SK Mobile School",
      legalName: "SK Mobile School",
      description:
        "SK Mobile School - Professional mobile device repair training institute in Bangladesh. Offering comprehensive courses in smartphone, tablet repair. Learn from industry experts with hands-on training",
      url: "https://skmobileschool.com",
      logo: "https://skmobileschool.com/images/logo.webp",
      email: "<EMAIL>",
      telephone: "+8801761-274120",

      address: {
        "@type": "PostalAddress",
        streetAddress: "Kanasat, Shibganj, Chapai Nawabganj",
        addressLocality: "Rajshahi",
        addressRegion: "Rajshahi Division",
        postalCode: "6341",
        addressCountry: "BD",
      },

      contactPoint: [
        {
          "@type": "ContactPoint",
          telephone: "+8801761-274120",
          contactType: "customer support",
          areaServed: "BD",
          availableLanguage: ["Bengali", "English"],
        },
      ],

      sameAs: [
        "https://www.facebook.com/skmobileschool",
        "https://www.facebook.com/share/1A3GyUuSrN/?mibextid=wwXIfr",
        "https://www.youtube.com/@SkMobileSchool1",
        "https://instagram.com/shohidullakwesar?igshid=YWYwM2I1ZDdmOQ==",
      ],
    },
  },
});
